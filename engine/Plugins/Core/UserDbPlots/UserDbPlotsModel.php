<?php

namespace TF\Engine\Plugins\Core\UserDbPlots;

use PDO;
use TF\Engine\Kernel\PdoHelper;
use TF\Engine\Plugins\Core\Filter\FilterGroups;
use TF\Engine\Plugins\Core\UserDb\UserDbModel;

class UserDbPlotsModel extends UserDbModel
{
    use FilterGroups;

    public function getPlotsContractsRelations($plotId, array $farmingIds = [], bool $includeRemovedPlots = false)
    {
        $table = $this->contractsPlotsRelTable;

        $sql = "SELECT cpr.*, ad.area 
            FROM 
                {$table} cpr
            JOIN {$this->tableContracts} c
                ON (cpr.contract_id = c.id)";

        $sql .= "
            LEFT JOIN {$this->tableAgreements} a
		        ON (cpr.contract_id = a.contract_id)
            LEFT JOIN {$this->tableAgreementsData} ad 
                ON(a.id = ad.agreement_id and cpr.plot_id = ad.gid)
		    WHERE true ";

        if (count($farmingIds) > 0) {
            $sql .= ' AND c.farming_id IN (' . implode(',', $farmingIds) . ')';
        }

        if (!$includeRemovedPlots) {
            $sql .= " AND cpr.annex_action = 'added'";
        }

        if (isset($plotId)) {
            $sql .= ' AND cpr.plot_id = :plot_id';
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($plotId)) {
            $cmd->bindParameter(':plot_id', $plotId);
        }

        if (isset($kcUid)) {
            $cmd->bindParameter(':keycloak_uid', $kcUid);
        }

        return $cmd->query()->readAll();
    }

    public function getPlotDataByContractFilter($options, $counter, $returnOnlySQL)
    {
        if (!$options['return']) {
            $return = '*, ST_ASTEXT(geom)';
        }

        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $sql = "SELECT {$return} FROM {$options['tablename']} p
					INNER JOIN {$this->contractsPlotsRelTable} r ON(p.gid = r.plot_id)
					INNER JOIN {$this->tableContracts} c ON(r.contract_id = c.id)
						WHERE true";

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if (!$counter && $options['sort']) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getContractsDataForPlots($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return} FROM " . $this->tableContracts . ' WHERE CASE WHEN is_annex = true AND active = false THEN nm_usage_rights <> 1 ELSE true END ';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if (isset($options['id_string'])) {
            $id_string = $options['id_string'];
            $sql .= " AND id IN ({$id_string})";
        }
        if (isset($options['anti_id_string'])) {
            $anti_id_string = $options['anti_id_string'];
            $sql .= " AND id NOT IN ({$anti_id_string})";
        }
        if ($options && !$counter) {
            if (!empty($options['sort'])) {
                $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
            }

            $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
            $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

            if (false !== $limit && false !== $offset) {
                $sql .= $returnOnlySQL
                    ? " LIMIT {$limit} OFFSET {$offset}"
                    : ' LIMIT :limit OFFSET :offset';
            }
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function KVSMultiEdit($options)
    {
        $updateFields = array_keys($options['update']);
        $updateOptions = array_values($options['update']);

        $sql = "UPDATE {$this->tableKVS} main_kvs SET ";

        for ($i = 0; $i < count($updateFields); $i++) {
            // check if variable is for binding or not
            if (true == $updateOptions[$i]['bind']) {
                $sql .= "{$updateFields[$i]} = :{$updateFields[$i]}";
            } else {
                $sql .= "{$updateFields[$i]} = {$updateOptions[$i]['value']}";
            }

            if ($i < count($updateFields) - 1) {
                $sql .= ', ';
            }
        }

        $sql .= ' WHERE gid IN(' . $options['id_string'] . ')';

        $cmd = $this->DbModule->createCommand($sql);

        for ($i = 0; $i < count($updateFields); $i++) {
            if (true == $updateOptions[$i]['bind']) {
                $parameterType = PdoHelper::getPdoType($updateOptions[$i]['value']);
                $cmd->bindParameter(':' . $updateFields[$i], $updateOptions[$i]['value'], $parameterType);
            }
        }

        $cmd->execute();
    }

    public function getPlotData($options, $counter, $returnOnlySQL)
    {
        if (!$options['return']) {
            $options['return'] = ['*', 'ST_ASTEXT(geom)'];
        }

        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return} FROM " . $options['tablename'] . ' p WHERE true';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['id_string']) {
            $sql .= " AND p.gid IN ({$options['id_string']})";
        }

        if ($options['anti_id_string']) {
            $sql .= " AND p.gid NOT IN ({$options['anti_id_string']})";
        }

        if (!$counter && $options['sort']) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getPlotAreaReport($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $sql = '';
        // With UNION to take correct "разделени или обединени имоти в справките" according to "edit_active_from"
        if ($counter) {
            $sql = 'SELECT sum(count) AS count FROM (';
        }
        $sql .= '( SELECT ' . $return . ' FROM ' . $options['tablename'] . ' p'
                . ' LEFT JOIN ' . $this->contractsPlotsRelTable . ' r ON(r.plot_id = p.gid)'
                . ' LEFT JOIN ' . $this->tableContracts . ' c ON(r.contract_id = c.id)'
                . ' LEFT JOIN ' . $this->plotsOwnersRelTable . ' re ON(re.pc_rel_id = r.id)'
                . ' LEFT JOIN ' . $this->tableOwners . ' o ON(o.id = re.owner_id) '
                . ' LEFT JOIN ' . $this->viewTopicLayerByOwnerNameLabelItems . ' tkvs on (tkvs.gid = p.gid)
				  WHERE p.is_edited = FALSE AND (p.edit_active_from <= now() OR p.edit_active_from IS NULL) ';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
            $sql = $this->createWhereSQL($sql, $options['contract_data'], $returnOnlySQL);
            $sql = $this->createWhereSQL($sql, $options['owner_data'], $returnOnlySQL);
        }

        if ($options['group'] && !$counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        $sql .= ') UNION
				( SELECT ' . $return . ' FROM ' . $options['tablename'] . ' p'
                . ' LEFT JOIN ' . $this->contractsPlotsRelTable . ' r ON(r.plot_id = p.gid)'
                . ' LEFT JOIN ' . $this->tableContracts . ' c ON(r.contract_id = c.id)'
                . ' LEFT JOIN ' . $this->plotsOwnersRelTable . ' re ON(re.pc_rel_id = r.id)'
                . ' LEFT JOIN ' . $this->tableOwners . ' o ON(o.id = re.owner_id) '
                . ' LEFT JOIN ' . $this->viewTopicLayerByOwnerNameLabelItems . ' tkvs on (tkvs.gid = p.gid)
				  WHERE p.is_edited = TRUE AND p.edit_active_from > now() ';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
            $sql = $this->createWhereSQL($sql, $options['contract_data'], $returnOnlySQL);
            $sql = $this->createWhereSQL($sql, $options['owner_data'], $returnOnlySQL);
        }

        if ($options['group'] && !$counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }
        if (!$counter && $options['sort']) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        $sql .= ')';
        if ($counter) {
            $sql .= ') data';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
            $this->createWhereBinds($cmd, $options['contract_data']);
            $this->createWhereBinds($cmd, $options['owner_data']);
        }

        return $cmd->query()->readAll();
    }

    public function getPlotsArea($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $sql = "SELECT {$return} FROM {$options['tablename']} kvs"
        // .' LEFT JOIN ' . $this->contractsPlotsRelTable . ' pc ON(pc.plot_id = kvs.gid)'
        . ' WHERE TRUE';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }
        if ($options['in_string']) {
            $inString = $options['in_string'];

            if ($inString['column'] && $inString['value'] && '' != $inString['value']) {
                $sql .= " AND {$inString['column']} IN ({$inString['value']})";
            }
        }

        if ($options['group']) {
            $sql .= ' GROUP BY ' . $options['group'];
        }
        if (!$counter && $options['sort']) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getPlotDataForDeclaration($options, $counter, $returnOnlySQL)
    {
        if ($counter) {
            $return = 'COUNT(DISTINCT(p.gid))';
        } elseif ($options['return']) {
            $return = implode(',', $options['return']);
        } else {
            $return = '*';
        }

        if ($options['join']) {
            $join = $options['join'];
        } else {
            $join = 'INNER';
        }

        $sql = "SELECT {$return} FROM {$options['tablename']} p
					{$join} JOIN {$this->contractsPlotsRelTable} r ON(r.plot_id = p.gid)
					{$join} JOIN {$this->tableContracts} c ON(r.contract_id = c.id)";

        $sql .= ' WHERE TRUE';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], true);
        }

        if ($options['id_string']) {
            $sql .= " AND p.gid IN ({$options['id_string']})";
        }

        if ($options['anti_id_string']) {
            $sql .= " AND p.gid NOT IN ({$options['anti_id_string']})";
        }

        if ($options['sort'] && $options['order'] && !$counter) {
            $sql .= " ORDER BY {$options['sort']} {$options['order']}";
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function updatePlotContractStatus($id_string, $status)
    {
        $sql = "UPDATE {$this->tableKVS} SET
					has_contracts = :status
				WHERE gid IN({$id_string})";

        $cmd = $this->DbModule->createCommand($sql);

        $cmd->bindParameter(':status', $status);

        $cmd->execute();
    }

    public function getFullPlotData($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);
        $sql = '';

        if (count($options['cte'])) {
            $sql .= $options['cte'];
        }

        $sql .= "SELECT {$return} FROM";
        $sql .= '(SELECT 
                    pc.id as cpr_id, 
                    po.id as por_id, 
                    po.percent as po_percent, 
                    c.id as c_id, 
                    c.is_sublease,
                    c.is_annex,
                    c.active,
                    c.start_date,
                    c.due_date,
                    c.c_num,
                    c.na_num,
                    c.tom,
                    c.delo,
                    c.court,
                    c.farming_id,
                    c.farming_name,
                    get_contract_status(c.id, c.active, c.start_date, c.due_date) AS contract_status_text,
                    o.id as o_id, 
                    kvs.ekate as kvs_ekate, 
                    kvs.virtual_ekatte_name,
                    kvs.masiv as kvs_masiv, 
                    kvs.number as kvs_number, 
                    kvs.kad_ident as kvs_kad_ident, 
                    kvs.geom as kvs_geom, 
                    kvs.gid as kvs_gid,
                    kvs.kad_ident,
                    kvs.old_kad_ident,
                    kvs.area_type, 
                    kvs.virtual_ntp_title,
                    kvs.category,
                    kvs.virtual_category_title,
                    kvs.mestnost,
                    kvs."number",
                    kvs.is_edited,
                    pc.contract_area,
                    pc.area_for_rent,
                    pc.kvs_allowable_area,
                    pc.comment as pc_comment,
                    kvs.document_area,
                    kvs.allowable_area,
                    kvs.masiv,
                    pc.rent_per_plot,
                    c.renta,
                    pc.annex_action,
                    c.nm_usage_rights,
                    o.is_dead,
                    kvs.comment as kvs_comment
                  FROM layer_kvs kvs'
                . ' INNER JOIN su_contracts_plots_rel pc ON(pc.plot_id = kvs.gid)'
                . ' INNER JOIN su_contracts c ON(c.id = pc.contract_id)'
                . ' LEFT JOIN su_plots_owners_rel po ON(po.pc_rel_id = pc.id)'
                . ' LEFT JOIN su_owners o ON(o.id = po.owner_id)'
                . ' LEFT JOIN su_owners_reps o_r on (o_r.owner_id = o.id) ';
        if (!$options['no_mat_view_join']) {
            $sql .= " LEFT JOIN {$this->viewTopicLayerByOwnerNameLabelItems} tkvs on (tkvs.gid = kvs.gid)";
        }

        if (array_key_exists('include_archived_plots', $options) && true === $options['include_archived_plots']) {
            $sql .= ') data ';
        } else {
            $sql .= ' WHERE (
                (kvs.edit_active_from::date > now() and kvs.is_edited = true)
            or 
                ((kvs.edit_active_from <= now()	or kvs.edit_active_from is null)and kvs.is_edited = false)
            ) = true ) data ';
        }

        if (!empty($options['joins']) || is_array($options['joins'])) {
            foreach ($options['joins'] as $join) {
                $sql .= ' ' . $join . ' ';
            }
        }
        $sql .= ' WHERE true ';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ('' != $options['pc_rel_id_string']) {
            $sql .= " AND cpr_id IN({$options['pc_rel_id_string']})";
        }
        if ('' != $options['contracts_id_string']) {
            $sql .= " AND c_id IN({$options['contracts_id_string']})";
        }
        if ('' != $options['plots_id_string']) {
            $sql .= " AND kvs_gid IN({$options['plots_id_string']})";
        }

        if ('' != $options['pc_rel_anti_id_string']) {
            $sql .= " AND cpr_id NOT IN({$options['pc_rel_anti_id_string']})";
        }
        if ('' != $options['contracts_anti_id_string']) {
            $sql .= " AND c_id NOT IN({$options['contracts_anti_id_string']})";
        }
        if ('' != $options['plots_anti_id_string']) {
            $sql .= " AND kvs_gid NOT IN({$options['plots_anti_id_string']})";
        }

        if ($options['whereOr']) {
            $sql = $this->createWhereOrSQL($sql, $options['whereOr'], $returnOnlySQL);
        }

        if ($options['group'] && !$counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($options['having'] && !$counter) {
            $sql .= ' HAVING ' . $options['having'];
        }

        if (!$counter && $options['sort']) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        if ($options['whereOr']) {
            $this->createWhereBinds($cmd, $options['whereOr']);
        }

        return $cmd->query()->readAll();
    }

    public function getSubleasedPlotData($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $sql = " SELECT {$return} FROM layer_kvs kvs"
                . ' INNER JOIN su_contracts_plots_rel pc ON(pc.plot_id = kvs.gid)'
                . ' INNER JOIN su_subleases_plots_contracts_rel spcr ON(spcr.pc_rel_id = pc.id)'
                . ' INNER JOIN su_contracts c ON(c.id = spcr.sublease_id)'
                . ' LEFT JOIN su_plots_owners_rel po ON(po.pc_rel_id = pc.id)'
                . ' LEFT JOIN su_owners o ON(o.id = po.owner_id)'
                . ' LEFT JOIN su_subleases_plots_area sspa ON sspa.sublease_id = spcr.sublease_id AND sspa.plot_id = pc.plot_id'
                . ' WHERE true';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ('' != $options['pc_rel_id_string']) {
            $sql .= " AND pc.id IN({$options['pc_rel_id_string']})";
        }
        if ('' != $options['contracts_id_string']) {
            $sql .= " AND c.id IN({$options['contracts_id_string']})";
        }
        if ('' != $options['plots_id_string']) {
            $sql .= " AND kvs.gid IN({$options['plots_id_string']})";
        }

        if ('' != $options['pc_rel_anti_id_string']) {
            $sql .= " AND pc.id NOT IN({$options['pc_rel_anti_id_string']})";
        }
        if ('' != $options['contracts_anti_id_string']) {
            $sql .= " AND c.id NOT IN({$options['contracts_anti_id_string']})";
        }
        if ('' != $options['plots_anti_id_string']) {
            $sql .= " AND kvs.gid NOT IN({$options['plots_anti_id_string']})";
        }

        if ($options['group'] && !$counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($options['having'] && !$counter) {
            $sql .= ' HAVING ' . $options['having'];
        }

        if (!$counter && $options['sort']) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getFullPlotDataForReport($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $sql = '';
        // With UNION to take correct "разделени или обединени имоти в справките" according to "edit_active_from"
        if ($counter) {
            $sql = 'SELECT sum(count) AS count FROM (';
        }
        if ($options['extent']) {
            $sql = "SELECT {$options['extent']} FROM (";
        }
        $sql .= "( SELECT {$return} FROM su_contracts c"
                . ' LEFT JOIN su_contracts a ON(a.parent_id = c.id AND a.active = true)';

        if ($options['start_date']) {
            $sql .= ' AND a.start_date <= :start_date ';
        }
        if ($options['due_date']) {
            $sql .= ' AND a.due_date >= :due_date ';
        }

        $sql .= ' INNER JOIN su_contracts_plots_rel pc ON(pc.contract_id = (CASE WHEN a.id IS NULL THEN c.id ELSE a.id END))'
                . ' INNER JOIN layer_kvs kvs ON(kvs.gid = pc.plot_id)'
                . ' LEFT JOIN su_plots_farming_rel pf ON(pf.pc_rel_id = pc.id)'
                . ' LEFT JOIN su_plots_owners_rel po ON(po.pc_rel_id = pc.id)'
                . ' LEFT JOIN su_owners o ON(o.id = po.owner_id)'
                . ' LEFT JOIN su_owners_reps r ON(r.id = po.rep_id)'
                . ' LEFT JOIN su_sales_contracts_plots_rel scpr on (scpr.contract_id = c.id)'
                . ' LEFT JOIN su_sales_contracts sc on (scpr.contract_id = sc.id)'
                . ' WHERE kvs.is_edited = FALSE
				          AND (kvs.edit_active_from <= now() OR kvs.edit_active_from IS NULL)
						  AND gid NOT IN
						  	(
						  		(
						  			SELECT plot_id FROM su_subleases_plots_contracts_rel spcr
					  				INNER JOIN su_contracts_plots_rel cpr on cpr.id = spcr.pc_rel_id
					  				WHERE spcr.pc_rel_id = pc. ID
					  			)';
        if ($options['due_date']) {
            $sql .= '				UNION
									(
										SELECT plot_id
										FROM su_sales_contracts sc
										LEFT JOIN su_sales_contracts_plots_rel scpr ON (scpr.sales_contract_id = sc. ID)
										WHERE sc.start_date <= :due_date and plot_id is not null
									)';
        }
        $sql .= '			) ';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ('' != $options['pc_rel_id_string']) {
            $sql .= " AND pc.id IN({$options['pc_rel_id_string']})";
        }
        if ('' != $options['contracts_id_string']) {
            $sql .= " AND c.id IN({$options['contracts_id_string']})";
        }
        if ('' != $options['plots_id_string']) {
            $sql .= " AND kvs.gid IN({$options['plots_id_string']})";
        }

        if ('' != $options['pc_rel_anti_id_string']) {
            $sql .= " AND pc.id NOT IN({$options['pc_rel_anti_id_string']})";
        }
        if ('' != $options['contracts_anti_id_string']) {
            $sql .= " AND c.id NOT IN({$options['contracts_anti_id_string']})";
        }
        if ('' != $options['plots_anti_id_string']) {
            $sql .= " AND kvs.gid NOT IN({$options['plots_anti_id_string']})";
        }

        if ($options['start_date']) {
            $sql .= ' AND (c.start_date <= :start_date OR a.start_date <= :start_date)';
        }

        if ($options['due_date']) {
            $sql .= ' AND (c.due_date IS NULL OR c.due_date >= :due_date OR a.due_date >= :due_date)';
        }

        if ($options['group']) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($options['having']) {
            $sql .= ' having ' . $options['having'];
        }

        $sql .= ") UNION
				  (SELECT {$return} FROM su_contracts c"
                . ' LEFT JOIN su_contracts a ON(a.parent_id = c.id AND a.active = true)';

        if ($options['start_date']) {
            $sql .= ' AND a.start_date <= :start_date ';
        }
        if ($options['due_date']) {
            $sql .= ' AND a.due_date >= :due_date ';
        }

        $sql .= ' INNER JOIN su_contracts_plots_rel pc ON(pc.contract_id = (CASE WHEN a.id IS NULL THEN c.id ELSE a.id END))'
                . ' INNER JOIN layer_kvs kvs ON(kvs.gid = pc.plot_id)'
                . ' LEFT JOIN su_plots_farming_rel pf ON(pf.pc_rel_id = pc.id)'
                . ' LEFT JOIN su_plots_owners_rel po ON(po.pc_rel_id = pc.id)'
                . ' LEFT JOIN su_owners o ON(o.id = po.owner_id)'
                . ' LEFT JOIN su_owners_reps r ON(r.id = po.rep_id)'
                . ' LEFT JOIN su_sales_contracts_plots_rel scpr on (scpr.contract_id = c.id)'
                . ' LEFT JOIN su_sales_contracts sc on (scpr.contract_id = sc.id)'
                . ' WHERE kvs.is_edited = TRUE
						  AND kvs.edit_active_from > now()
						  AND gid NOT IN
						  	(
						  		(
						  			SELECT plot_id FROM su_subleases_plots_contracts_rel spcr
					  				INNER JOIN su_contracts_plots_rel cpr on cpr.id = spcr.pc_rel_id
					  				WHERE spcr.pc_rel_id = pc. ID
					  			)';
        if ($options['due_date']) {
            $sql .= '				UNION
									(
										SELECT plot_id
										FROM su_sales_contracts sc
										LEFT JOIN su_sales_contracts_plots_rel scpr ON (scpr.sales_contract_id = sc. ID)
										WHERE sc.start_date <= :due_date and plot_id is not null
									)';
        }
        $sql .= '			) ';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ('' != $options['pc_rel_id_string']) {
            $sql .= " AND pc.id IN({$options['pc_rel_id_string']})";
        }
        if ('' != $options['contracts_id_string']) {
            $sql .= " AND c.id IN({$options['contracts_id_string']})";
        }
        if ('' != $options['plots_id_string']) {
            $sql .= " AND kvs.gid IN({$options['plots_id_string']})";
        }

        if ('' != $options['pc_rel_anti_id_string']) {
            $sql .= " AND pc.id NOT IN({$options['pc_rel_anti_id_string']})";
        }
        if ('' != $options['contracts_anti_id_string']) {
            $sql .= " AND c.id NOT IN({$options['contracts_anti_id_string']})";
        }
        if ('' != $options['plots_anti_id_string']) {
            $sql .= " AND kvs.gid NOT IN({$options['plots_anti_id_string']})";
        }

        if ($options['start_date']) {
            $sql .= ' AND (c.start_date <= :start_date OR a.start_date <= :start_date)';
        }

        if ($options['due_date']) {
            $sql .= ' AND (c.due_date IS NULL OR c.due_date >= :due_date OR a.due_date >= :due_date)';
        }

        if ($options['group']) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($options['having']) {
            $sql .= ' having ' . $options['having'];
        }

        $sql .= ')';

        if (!$counter && $options['sort']) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        if ($options['extent']) {
            $sql .= ') data';
        }
        if ($counter) {
            $sql .= ') data';
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            $sql = str_replace(':start_date', "'" . $options['start_date'] . "'", $sql);

            return str_replace(':due_date', "'" . $options['due_date'] . "'", $sql);
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['start_date']) {
            $cmd->bindParameter(':start_date', $options['start_date']);
        }
        if ($options['due_date']) {
            $cmd->bindParameter(':due_date', $options['due_date']);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getSubleasedPlotsReport($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $sql = '';
        // With UNION to take correct "разделени или обединени имоти в справките" according to "edit_active_from"
        if ($counter) {
            $sql = 'SELECT sum(count) AS count FROM (';
        }
        if ($options['extent']) {
            $sql = "SELECT {$options['extent']} FROM (";
        }

        if ('true' == $options['where']['createdPlots']) {
            $checkDate = 'c.start_date';
        }
        $sql .= "( SELECT {$return} FROM {$this->tableKVS} kvs"
            . " INNER JOIN {$this->contractsPlotsRelTable} pc ON(pc.plot_id = kvs.gid)"
            . " INNER JOIN {$this->tableSubleasesPlotsContractsRel} spc ON(spc.pc_rel_id = pc.id)"
            . " LEFT JOIN {$this->tableSubleasesPlotsArea} spa on spa.plot_id = pc.plot_id and spa.sublease_id = spc.sublease_id "
            . " INNER JOIN {$this->tableContracts} c ON(c.id = spc.sublease_id)"
            . " INNER JOIN {$this->tableContracts} c1 ON(c1.id = pc.contract_id)"
            . " LEFT JOIN {$this->tableContractsContragents} cc ON(cc.contract_id = spc.sublease_id)"
            . " LEFT JOIN {$this->tableOwners} co ON(co.id = cc.owner_id)"
            . " LEFT JOIN {$this->plotsOwnersRelTable} po ON(po.pc_rel_id = pc.id)"
            . " LEFT JOIN {$this->tableOwners} o ON(o.id = po.owner_id)"
            . " LEFT JOIN {$this->tableContractsFarmingContragents} fc ON(fc.contract_id = spc.sublease_id)"
            . " LEFT JOIN {$this->tableContracts} a on(a.parent_id = c1.id AND a.active = true)"
            . ' LEFT JOIN (select scpr.contract_id, scpr.plot_id,
            SUM(scpr.contract_area_for_sale) as sold_area,
                    MAX(scpr.contract_area) as total_area FROM su_sales_contracts_plots_rel scpr';
        $sql .= ' INNER join su_sales_contracts sc on(sc.id = scpr.sales_contract_id ';
        if ($options['start_date']) {
            $sql .= ' and sc.start_date <= :start_date) ';
        } else {
            $sql .= ' ) ';
        }
        $sql .= 'group by scpr.contract_id, scpr.plot_id ) AS t2
        ON (t2.contract_id = c.id	AND t2.plot_id = pc.plot_id) '
            . ' WHERE case when t2.total_area is not null then t2.total_area > t2.sold_area else c.id > 0 end
				    AND kvs.is_edited = FALSE AND (kvs.edit_active_from <= ' . ($checkDate ? $checkDate : 'now()') . ' OR kvs.edit_active_from IS NULL) ';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['whereOr']) {
            $sql = $this->createWhereOrSQL($sql, $options['whereOr'], $returnOnlySQL);
        }

        if (!empty($options['whereOrGroup'])) {
            $sql = $this->createWhereOrGroupSQL($sql, $options['whereOrGroup'], $returnOnlySQL);
        }

        if ($options['group'] && !$counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($options['having'] && !$counter) {
            $sql .= ' having ' . $options['having'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }
        $sql .= ')';

        if (!$counter && $options['sort']) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }
        if ($options['extent']) {
            $sql .= ') data';
        }
        if ($counter) {
            $sql .= ') data';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }
        if ($options['whereOr']) {
            $this->createWhereBinds($cmd, $options['whereOr']);
        }

        if ($options['start_date']) {
            $cmd->bindParameter(':start_date', $options['start_date'], PDO::PARAM_STR);
        }
        if ($options['where']['start_date']) {
            $cmd->bindParameter(':start_date', $options['where']['start_date']['value'], PDO::PARAM_STR);
        }

        return $cmd->query()->readAll();
    }

    public function getHypothecsPlotsReport($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $sql = '';
        // With UNION to take correct "разделени или обединени имоти в справките" according to "edit_active_from"
        if ($counter) {
            $sql = 'SELECT sum(count) AS count FROM (';
        }
        if ($options['extent']) {
            $sql = "SELECT {$options['extent']} FROM (";
        }
        $sql .= "( SELECT {$return} FROM {$this->tableKVS} kvs"
                . " INNER JOIN {$this->tableHypothecsPlotsRel} ph ON(ph.plot_id = kvs.gid)"
                . " INNER JOIN {$this->tableHypothecs} h ON(h.id = ph.hypothec_id)"
                . " INNER JOIN {$this->tableCreditors} c ON(c.id = h.creditor_id)"
                . ' WHERE kvs.is_edited = FALSE AND (kvs.edit_active_from <= now() OR kvs.edit_active_from IS NULL) ';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['group'] && !$counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        $sql .= ") UNION
				  (SELECT {$return} FROM {$this->tableKVS} kvs"
                . " INNER JOIN {$this->tableHypothecsPlotsRel} ph ON(ph.plot_id = kvs.gid)"
                . " INNER JOIN {$this->tableHypothecs} h ON(h.id = ph.hypothec_id)"
                . " INNER JOIN {$this->tableCreditors} c ON(c.id = h.creditor_id)"
                . ' WHERE kvs.is_edited = TRUE AND kvs.edit_active_from > now() ';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['group'] && !$counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        $sql .= ')';

        if (!$counter && $options['sort']) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($options['extent']) {
            $sql .= ') data';
        }

        if ($counter) {
            $sql .= ') data';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getPlotsData($options, $counter, $returnOnlySQL)
    {
        $sql = '';

        if (count($options['cte'])) {
            $sql .= $options['cte'];
        }

        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter'] ?? false);
        $arrCategory = [];
        $sql .= " SELECT {$return} FROM {$this->tableKVS} kvs"
                . " LEFT JOIN {$this->viewTopicLayerByOwnerNameLabelItems} tkvs on (tkvs.gid = kvs.gid)";

        if (!empty($options['joins']) || is_array($options['joins'])) {
            foreach ($options['joins'] as $join) {
                $sql .= ' ' . $join . ' ';
            }
        }

        $sql .= ' WHERE true ';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['whereOr']) {
            $sql = $this->createWhereOrSQL($sql, $options['whereOr'], $returnOnlySQL);
        }

        if (!empty($options['whereOrGroup'])) {
            $sql = $this->createWhereOrGroupSQL($sql, $options['whereOrGroup'], $returnOnlySQL);
        }

        if (isset($options['group']) && !$counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if (isset($options['having']) && !$counter) {
            $sql .= ' HAVING ' . $options['having'];
        }

        if (!$counter && $options['sort']) {
            $sql .= ' ORDER BY ' . $options['sort'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        if ($options['whereOr']) {
            $this->createWhereBinds($cmd, $options['whereOr']);
        }

        if (!empty($options['whereOrGroup'])) {
            $this->createWhereGroupBinds($cmd, $options['whereOrGroup']);
        }

        // bind Category
        if (count($arrCategory)) {
            for ($i = 0; $i < count($arrCategory); $i++) {
                $cmd->bindParameter(':' . $arrCategory[$i], $arrCategory[$i]);
            }
        }

        return $cmd->query()->readAll();
    }

    public function getDataForPlotsTree($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter'] ?? false);
        $arrCategory = [];
        $sql = " SELECT {$return} FROM {$this->tableKVS} kvs"
                . " LEFT JOIN {$this->contractsPlotsRelTable} pc ON(pc.plot_id = kvs.gid)"
                . " LEFT JOIN {$this->tableContracts} c ON(c.id = pc.contract_id)"
                . " LEFT JOIN {$this->tablePayments} p ON(p.contract_id = c.id)"
                . " LEFT JOIN {$this->plotsOwnersRelTable} po ON(po.pc_rel_id = pc.id)"
                . " LEFT JOIN {$this->tableOwners} o ON(o.id = po.owner_id)"
                . " LEFT JOIN {$this->tableOwnersReps} o_r ON(o_r.id = po.rep_id)"
                . " LEFT JOIN {$this->viewTopicLayerByOwnerNameLabelItems} tkvs on (tkvs.gid = kvs.gid)";

        if (!empty($options['joins']) || is_array($options['joins'])) {
            foreach ($options['joins'] as $join) {
                $sql .= ' ' . $join . ' ';
            }
        }

        $sql .= ' WHERE true ';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['whereOr']) {
            $sql = $this->createWhereOrSQL($sql, $options['whereOr'], $returnOnlySQL);
        }

        if (!empty($options['whereOrGroup'])) {
            $sql = $this->createWhereOrGroupSQL($sql, $options['whereOrGroup'], $returnOnlySQL);
        }

        if (isset($options['group']) && !$counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if (isset($options['having']) && !$counter) {
            $sql .= ' HAVING ' . $options['having'];
        }

        if (!$counter && $options['sort'] && $options['order']) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        if ($options['whereOr']) {
            $this->createWhereBinds($cmd, $options['whereOr']);
        }

        if (!empty($options['whereOrGroup'])) {
            $this->createWhereGroupBinds($cmd, $options['whereOrGroup']);
        }

        // bind Category
        if (count($arrCategory)) {
            for ($i = 0; $i < count($arrCategory); $i++) {
                $cmd->bindParameter(':' . $arrCategory[$i], $arrCategory[$i]);
            }
        }

        return $cmd->query()->readAll();
    }

    public function getDataForPlotsExtent($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);
        $arrCategory = [];
        $sql = " SELECT {$return} FROM {$this->tableKVS} kvs";

        $sql .= ' WHERE true ';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        // checks category with 'OR' clause
        if (count($options['category'])) {
            $key = array_search('-1', $options['category']);

            $withoutCategorySelected = false;

            // if selected "Без категория"
            if (false !== $key) {
                unset($options['category'][$key]);

                $withoutCategorySelected = true;
            }

            $arrCategory = array_values($options['category']);

            if (count($arrCategory)) {
                $sql .= ' AND (category IN(';

                for ($i = 0; $i < count($arrCategory); $i++) {
                    $sql .= ':' . $arrCategory[$i];

                    if ($i < count($arrCategory) - 1) {
                        $sql .= ', ';
                    }
                }

                $sql .= ')';

                if ($withoutCategorySelected) {
                    $sql .= ' OR category IS NULL)';
                } else {
                    $sql .= ')';
                }
            } else {
                $sql .= ' AND category IS NULL';
            }
        }

        if ($options['group'] && !$counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($options['having'] && !$counter) {
            $sql .= ' HAVING ' . $options['having'];
        }

        if (!$counter && $options['sort'] && $options['order']) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        // bind Category
        if (count($arrCategory)) {
            for ($i = 0; $i < count($arrCategory); $i++) {
                $cmd->bindParameter(':' . $arrCategory[$i], $arrCategory[$i]);
            }
        }

        return $cmd->query()->readAll();
    }

    public function getDataForPlotsTreeCounts($options, $counter, $returnOnlySQL)
    {
        $returnCount = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $return = $this->createReturnVariable($options['return'], false);

        $sql = " SELECT {$returnCount} FROM (
					SELECT {$return} FROM {$this->tableKVS} kvs
					LEFT JOIN {$this->contractsPlotsRelTable} pc ON(pc.plot_id = kvs.gid)
					LEFT JOIN {$this->tableContracts} c ON(c.id = pc.contract_id)
					LEFT JOIN {$this->plotsOwnersRelTable} po ON(po.pc_rel_id = pc.id)
					LEFT JOIN {$this->tableOwners} o ON(o.id = po.owner_id)
					LEFT JOIN {$this->tableOwnersReps} o_r ON(o_r.id = po.rep_id)
					LEFT JOIN {$this->viewTopicLayerByOwnerNameLabelItems} tkvs on (tkvs.gid = kvs.gid)
					WHERE true ";

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        // checks category with 'OR' clause
        if (count($options['category'])) {
            $key = array_search('-1', $options['category']);

            $withoutCategorySelected = false;

            // if selected "Без категория"
            if (false !== $key) {
                unset($options['category'][$key]);

                $withoutCategorySelected = true;
            }

            $arrCategory = array_values($options['category']);

            if (count($arrCategory)) {
                $sql .= ' AND (category IN(';

                for ($i = 0; $i < count($arrCategory); $i++) {
                    $sql .= ':' . $arrCategory[$i];

                    if ($i < count($arrCategory) - 1) {
                        $sql .= ', ';
                    }
                }

                $sql .= ')';

                if ($withoutCategorySelected) {
                    $sql .= ' OR category IS NULL)';
                } else {
                    $sql .= ')';
                }
            } else {
                $sql .= ' AND category IS NULL';
            }
        }

        if ($options['group']) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        $sql .= ') as kvs';

        $cmd = $this->DbModule->createCommand($sql);

        if ($returnOnlySQL) {
            return $sql;
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        // bind Category
        if (count($arrCategory)) {
            for ($i = 0; $i < count($arrCategory); $i++) {
                $cmd->bindParameter(':' . $arrCategory[$i], $arrCategory[$i]);
            }
        }

        return $cmd->query()->readAll();
    }

    public function getEditedPlots($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $sql = " SELECT {$return} FROM {$this->tableKvsEditLog} kvs_log"
                . " JOIN {$this->tableKVS} kvs_new ON(kvs_new.gid = kvs_log.new_gid)"
                . " JOIN {$this->tableKVS} kvs_old ON(kvs_old.gid = kvs_log.old_gid)"
                . ' WHERE true';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['group'] && !$counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if (!$counter && $options['sort']) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getFullPlotDataForDeclaration($options, $counter, $returnOnlySQL)
    {
        if (true === $counter) {
            $return = 'COUNT(*)';
        } elseif ($options['return']) {
            $return = implode(', ', $options['return']);
        } else {
            $return = '*';
        }

        $sql = $this->selectPlotSqlCreator($options, $return, $counter, $returnOnlySQL);

        if ($options['union']) {
            if (!empty($options['sub_pc_rel_anti_id_string'])) {
                unset($options['sub_pc_rel_anti_id_string']);
            }
            if (true == $counter) {
                $return = 'COUNT(*),SUM(contract_area) as total_area, SUM (CASE WHEN kvs.irrigated_area = true THEN kvs.document_area ELSE 0 END ) as irrigated_area';
            } elseif ($options['union']['return']) {
                $return = implode(', ', $options['union']['return']);
            } else {
                $return = '*';
            }
            if (!empty($options['union']['having'])) {
                $options['having'] = $options['union']['having'];
            }
            if (!empty($options['union']['pc_rel_id_string'])) {
                unset($options['pc_rel_id_string']);
                $options['sub_pc_rel_id_string'] = $options['union']['pc_rel_id_string'];
            }
            if (!empty($options['union']['pc_rel_anti_id_string'])) {
                $options['pc_rel_anti_id_string'] = $options['union']['pc_rel_anti_id_string'];
            }
            if (!empty($options['union']['sub_pc_rel_anti_id_string'])) {
                $options['sub_pc_rel_anti_id_string'] = $options['union']['sub_pc_rel_anti_id_string'];
            }
            if (!empty($options['union']['group'])) {
                $options['group'] = $options['union']['group'];
            }

            if (!empty($options['union']['joins'])) {
                $options['joins'] = array_merge($options['joins'], $options['union']['joins']);
            }

            if (!empty($options['union']['where'])) {
                $options['where'] = array_merge($options['where'], $options['union']['where']);
            }

            $sql .= ' UNION ALL ';
            $sql .= $this->selectPlotSqlCreator($options, $return, $counter, $returnOnlySQL);
        }

        $sql = 'select count(*) OVER () as total_count, * from (' . $sql . ') a ';

        if ($options['order'] && $options['sort'] && !$counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            $sql = str_replace(':farming_start_date', "'" . $options['farming_start_date'] . "'", $sql);
            $sql = str_replace(':farming_due_date', "'" . $options['farming_due_date'] . "'", $sql);

            return str_replace(':today', "'" . $options['today'] . "'", $sql);
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['farming_start_date']) {
            $cmd->bindParameter(':farming_start_date', $options['farming_start_date']);
        }
        if ($options['farming_due_date']) {
            $cmd->bindParameter(':farming_due_date', $options['farming_due_date']);
        }
        if ($options['today']) {
            $cmd->bindParameter(':today', $options['today']);
        }
        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        if ($options['whereOr']) {
            $this->createWhereBinds($cmd, $options['whereOr']);
        }

        return $cmd->query()->readAll();
    }

    public function getAgreementsPlotsFromList($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $sql = " SELECT {$return} FROM {$this->contractsPlotsRelTable} pc"
        . " JOIN {$this->tableContracts} c ON(c.id = pc.contract_id)"
        . ' WHERE true';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ('' != $options['plots_anti_id_string']) {
            $sql .= " AND pc.plot_id IN({$options['plots_anti_id_string']})";
        }

        if ($options['group'] && !$counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if (!$counter && $options['sort']) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getExistingEkateInKMS($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $sql = " SELECT {$return} FROM {$options['kms_table']} kms"
                . ' WHERE true';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ('' != $options['plots_anti_id_string']) {
            $sql .= " AND pc.plot_id IN({$options['plots_anti_id_string']})";
        }

        if ($options['group'] && !$counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if (!$counter && $options['sort']) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getPlotNeighbours($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $sql = " SELECT {$return} FROM layer_kvs kvs"
            . ' JOIN layer_kvs kvs2 ON(kvs.gid != kvs2.gid and ST_intersects(kvs.geom,kvs2.geom))'
            . ' LEFT JOIN su_contracts_plots_rel pc ON(pc.plot_id = kvs2.gid and pc.contract_id = ' . $options['contract_id'] . ')'
            . ' WHERE true'
            . " AND ST_GeometryType(ST_INTERSECTION(kvs.geom,kvs2.geom)) != 'ST_Point'"
            . " AND kvs.kad_ident = '" . $options['kad_ident'] . "'";

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getPlotSalesContractData($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $sql = " SELECT {$return} FROM su_sales_contracts sc"
                . ' INNER JOIN su_sales_contracts_plots_rel scpr on scpr.sales_contract_id = sc.id'
                . ' INNER JOIN layer_kvs kvs on kvs.gid = scpr.plot_id'
                . ' WHERE true';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['group'] && !$counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($options['having'] && !$counter) {
            $sql .= ' HAVING ' . $options['having'];
        }

        if (!$counter && $options['sort']) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getPlotsInSubleases($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $sql = "SELECT {$return} FROM su_contracts c ";

        $sql .= ' LEFT JOIN su_contracts a ON (a.parent_id = c.id AND a.active = true)';

        if ('' != $options['start_date_on'] && '' != $options['due_date_on']) {
            $sql .= " INNER JOIN su_contracts_plots_rel pc ON(pc.contract_id = (CASE WHEN a.id IS NULL THEN c.id ELSE
								(
							CASE
							WHEN (
								A .start_date <= '" . $options['start_date_on'] . "'
								AND A .due_date >= '" . $options['due_date_on'] . "'
							) THEN
								A . ID
							ELSE
								C . ID
							END
						) END))";
        } else {
            $sql .= ' INNER JOIN su_contracts_plots_rel pc ON(pc.contract_id = (CASE WHEN a.id IS NULL THEN c.id ELSE a.id END))';
        }

        $sql .= ' LEFT JOIN layer_kvs kvs ON (pc.plot_id = kvs.gid)';
        $sql .= " LEFT JOIN {$this->tableEkatteCombobox} ekate ON (ekate.ekate = kvs.ekate)";

        $sql .= 'LEFT join su_subleases_plots_contracts_rel AS spcr ON (spcr.pc_rel_id = pc.id)'
            . "LEFT join su_contracts AS sc ON (sc.id = spcr.sublease_id and sc.active = true and sc.is_sublease = true and  tsrange(sc.start_date, sc.due_date ,'[]') && tsrange('" . $options['start_date_on'] . "', '" . $options['due_date_on'] . "', '[]')	)"
            . 'LEFT join su_subleases_plots_area AS sspa ON (sspa.sublease_id = sc.id and sspa.plot_id = pc.plot_id)'
            . "LEFT JOIN LATERAL (
                    SELECT
                        sscpr.id,
                        sscpr.pc_rel_id,
                        sscpr.contract_area_for_sale,
                        sscpr.contract_area
                    FROM
                        su_sales_contracts_plots_rel sscpr
                    LEFT JOIN su_sales_contracts ssc ON
                        sscpr.sales_contract_id = ssc.id
                        AND sscpr.pc_rel_id = pc.id
                    WHERE
                        ssc.start_date <= '{$options['start_date_on']}'
            ) sales_c ON sales_c.pc_rel_id = pc.id"
            . ' WHERE true ';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ('' != $options['pc_rel_id_string']) {
            $sql .= " AND pc.id IN({$options['pc_rel_id_string']})";
        }
        if ('' != $options['contracts_id_string']) {
            $sql .= " AND c.id IN({$options['contracts_id_string']})";
        }
        if ('' != $options['plots_id_string']) {
            $sql .= " AND gid IN({$options['plots_id_string']})";
        }

        if ('' != $options['pc_rel_anti_id_string']) {
            $sql .= " AND pc.id NOT IN({$options['pc_rel_anti_id_string']})";
        }
        if ('' != $options['contracts_anti_id_string']) {
            $sql .= " AND c.id NOT IN({$options['contracts_anti_id_string']})";
        }
        if ('' != $options['plots_anti_id_string']) {
            $sql .= " AND gid NOT IN({$options['plots_anti_id_string']})";
        }

        if ($options['caseWhere']) {
            $sql .= $options['caseWhere'];
        }

        if ($options['group']) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($options['having']) {
            $sql .= ' HAVING ' . $options['having'];
        }

        if (!$counter && $options['sort']) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getAvailablePlotsToSublease($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $distinct = $options['distinct'] ?? 'DISTINCT ON (pc.id)';

        $sql = 'WITH subleases as (';
        $sql .= "SELECT 
                    {$distinct}
                    {$return}
                FROM su_contracts c 
                LEFT JOIN su_contracts a ON (a.parent_id = c.id AND a.active = true)
                LEFT JOIN su_contracts_plots_rel apc on apc.contract_id = a.id 
                INNER JOIN su_contracts_plots_rel pc ON pc.contract_id = c.id	
                LEFT JOIN su_subleases_plots_contracts_rel AS spcr ON (spcr.pc_rel_id = pc.id)
                LEFT JOIN layer_kvs kvs ON (pc.plot_id = kvs.gid)";

        if ('' != $options['start_date_on'] && '' != $options['due_date_on']) {
            $sql .= " LEFT join su_contracts AS sc ON (
                            sc.id = spcr.sublease_id
                            AND sc.active = true
                            AND sc.is_sublease = true
                            AND  tsrange(sc.start_date, sc.due_date ,'[]') && tsrange('" . $options['start_date_on'] . "', '" . $options['due_date_on'] . "', '[]')	
                    )";
        } else {
            $sql .= ' LEFT JOIN su_contracts AS sc ON (sc.id = spcr.sublease_id)';
        }

        $sql .= ' LEFT JOIN su_subleases_plots_area AS sspa ON (sspa.sublease_id = sc.id) and sspa.plot_id = pc.plot_id';
        $sql .= " LEFT JOIN LATERAL ( -- join sales contracts where only part of the plot is sold
            SELECT
                sscpr.id,
                sscpr.pc_rel_id,
                sscpr.contract_area_for_sale,
                sscpr.contract_area
            FROM
                su_sales_contracts_plots_rel sscpr
            LEFT JOIN su_sales_contracts ssc ON
                sscpr.sales_contract_id = ssc.id
                AND sscpr.pc_rel_id = pc.id
            WHERE
                ssc.start_date <= '{$options['start_date_on']}'
        ) sales_c 
            ON sales_c.pc_rel_id = pc.id
            AND sales_c.contract_area_for_sale < sales_c.contract_area
        ";
        $sql .= " LEFT JOIN {$this->tableEkatte} as ekattes on kvs.ekate = ekattes.ekatte_code";

        $sql .= ' LEFT JOIN su_plots_owners_rel po ON (po.pc_rel_id = pc.id)'
             . ' LEFT JOIN su_owners o ON(o.id = po.owner_id)'
             . ' WHERE true ';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ('' != $options['pc_rel_id_string']) {
            $sql .= " AND pc.id IN({$options['pc_rel_id_string']})";
        }
        if ('' != $options['contracts_id_string']) {
            $sql .= " AND c.id IN({$options['contracts_id_string']})";
        }
        if ('' != $options['plots_id_string']) {
            $sql .= " AND gid IN({$options['plots_id_string']})";
        }

        if ('' != $options['pc_rel_anti_id_string']) {
            $sql .= " AND pc.id NOT IN({$options['pc_rel_anti_id_string']})";
        }
        if ('' != $options['contracts_anti_id_string']) {
            $sql .= " AND c.id NOT IN({$options['contracts_anti_id_string']})";
        }
        if ('' != $options['plots_anti_id_string']) {
            $sql .= " AND gid NOT IN({$options['plots_anti_id_string']})";
        }

        if ($options['caseWhere']) {
            $sql .= $options['caseWhere'];
        }

        if ($options['group'] && !$counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if (!$counter && $options['sort']) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        $sql .= ')';

        $sql .= ' select
                    gid,
                   round(
                        (
                            sum(contract_area) - sum(contract_area_for_sale) - sum(subleased_area)
                        )::NUMERIC,
                        3
                    ) AS contract_area,
                    round(
                        (
                            sum(contract_area) - sum(contract_area_for_sale) - sum(subleased_area)
                        )::NUMERIC,
                        3
                    ) AS rent_area,
                    document_area,
                    sum(subleased_area) as subleased_area,	
                    plot_id,
                    kad_ident,
                    area_type,
                    category,
                    ekate,
                    is_edited,
                    ekatte_name,
                    used_area,
                    area,
                    array_agg(pc_rel_id) pc_rel_id_agg,
                    max(pc_rel_id) as pc_rel_id,
                    subleases_cnt
                from 
                    subleases
                group by
                    gid, plot_id, area_type, ekate, is_edited, ekatte_name, used_area, area, kad_ident, category, document_area, subleases_cnt
                ';

        if ($options['having'] && !$counter) {
            $sql .= ' HAVING ' . $options['having'];
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getOwnerlessPlots($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $sql = " SELECT {$return} FROM su_contracts_plots_rel pc"
                . ' LEFT OUTER JOIN su_plots_owners_rel po on(pc.id = po.pc_rel_id)'
                . ' LEFT OUTER JOIN su_plots_farming_rel pf on(pc.id = pf.pc_rel_id)'
                . ' LEFT JOIN su_contracts c on (c.id = pc.contract_id) '
                . ' INNER JOIN layer_kvs kvs on (pc.plot_id = kvs.gid) '
                . ' WHERE po.id IS NULL '
                . ' AND pf.id IS NULL ';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if (!empty($options['group']) && !$counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if (!empty($options['having']) && !$counter) {
            $sql .= ' HAVING ' . $options['having'];
        }

        if (!$counter && $options['sort']) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getHistoricalPlotsReportData($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $sql = " SELECT {$return} FROM su_contracts c"
                . ' LEFT JOIN su_contracts_plots_rel pc on (c.id = pc.contract_id) '
                . ' INNER JOIN layer_kvs kvs on (pc.plot_id = kvs.gid) '
                . ' WHERE true ';

        if ($options['due_date']) {
            $sql .= " AND (c.due_date >= '{$options['due_date']}' OR c.due_date IS NULL) ";
        }

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['group'] && !$counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($options['having'] && !$counter) {
            $sql .= ' HAVING ' . $options['having'];
        }

        if (!$counter && $options['sort']) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getRentedPlotsForReport($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $sql = '';
        // With UNION to take correct "разделени или обединени имоти в справките" according to "edit_active_from"
        if ($counter) {
            $sql = 'SELECT sum(count) AS count FROM (';
        }
        if ($options['extent']) {
            $sql = "SELECT {$options['extent']} FROM (";
        }
        $sql .= 'with 
            subleases_from_contracts as (
                SELECT
                C.c_num,
                C.start_date start_date,
                C.due_date due_date,
                C.start_date,
                cpr. ID cpr_id,
                spcr.sublease_id,
                cpr. ID pc_rel_id,
                sub. ID AS sub_id
                FROM
                    su_contracts C
                LEFT JOIN su_contracts_plots_rel cpr ON cpr.contract_id = C . ID
                LEFT JOIN su_subleases_plots_contracts_rel spcr ON spcr.pc_rel_id = cpr. ID
                LEFT JOIN su_contracts sub ON (
                    sub. ID = spcr.sublease_id
                    AND sub.is_sublease
                    AND sub.start_date <= :due_date
                    AND sub.due_date >= :start_date
                    AND sub.active = TRUE
                )
                WHERE
                    C.is_annex = FALSE
                AND C.is_sublease = FALSE
                AND c.nm_usage_rights <> 4
                AND spcr.sublease_id NOTNULL
                AND sub. ID NOTNULL
                AND (C.start_date <= :due_date and  C.due_date is null or C.due_date >= :start_date)
            ),
            subleases_from_annexes as (
                SELECT
                C.c_num,
                C.start_date start_date,
                C.due_date due_date,
                C.start_date,
                cpr. ID cpr_id,
                spcr.sublease_id,
                cpr. ID pc_rel_id,
                sub. ID AS sub_id
                FROM
                    su_contracts C
                LEFT JOIN su_contracts_plots_rel cpr ON cpr.contract_id = C . ID
                LEFT JOIN su_subleases_plots_contracts_rel spcr ON spcr.pc_rel_id = cpr. ID
                LEFT JOIN su_contracts sub ON (
                    sub. ID = spcr.sublease_id
                    AND sub.is_sublease
                    AND sub.start_date <= :due_date
                    AND sub.due_date >= :start_date
                    AND sub.active = TRUE
                )
                WHERE
                    C.is_annex = TRUE
                    AND C.is_sublease = FALSE
                    AND c.nm_usage_rights <> 4
                    AND spcr.sublease_id NOTNULL
                    AND sub. ID NOTNULL
                    AND (C.start_date <= :due_date and  C.due_date is null or C.due_date >= :start_date)
            )';

        // Getting plots from contracts
        $annexesDateFilter = '';
        if ($options['start_date']) {
            $annexesDateFilter .= ' AND a.start_date <= :due_date ';
        }

        if ($options['start_date']) {
            $annexesDateFilter .= ' AND a.due_date >= :start_date ';
        }

        $sql .= "( SELECT {$return}
				FROM su_contracts C
				INNER JOIN su_contracts_plots_rel cpr ON(cpr.contract_id = c.id)
				INNER JOIN {$this->tableContracts} c1 ON(c1.id = cpr.contract_id)
				LEFT JOIN layer_kvs kvs ON (cpr.plot_id = kvs.gid)
				LEFT JOIN su_plots_farming_rel PF ON(pf.pc_rel_id = cpr.id)
				LEFT JOIN su_plots_owners_rel PO ON(po.pc_rel_id = cpr.id)
				LEFT JOIN su_contracts a ON(c.id = a.parent_id {$annexesDateFilter} AND a.active = true)
				LEFT JOIN su_contracts c_a ON (c_a.id = c.parent_id)
				LEFT JOIN su_contracts_plots_rel c_a_rel ON (c_a_rel.contract_id = c_a.id and c_a_rel.plot_id = cpr.plot_id)
				LEFT JOIN su_subleases_plots_contracts_rel c_a_sub_plot_rel ON (c_a_sub_plot_rel.pc_rel_id = c_a_rel.id)
				LEFT JOIN su_contracts c_a_sub_c ON (c_a_sub_c.id = c_a_sub_plot_rel.sublease_id and c_a_sub_c.start_date <= :due_date and c_a_sub_c.due_date >= :start_date)
				LEFT JOIN su_owners O ON(o.id = po.owner_id)
                LEFT JOIN
                    (select scpr.contract_id, scpr.plot_id, SUM(scpr.contract_area_for_sale) as sold_area, MAX(scpr.contract_area) as total_area
                        FROM su_sales_contracts_plots_rel scpr
                        INNER join su_sales_contracts sc on(sc.id = scpr.sales_contract_id and sc.start_date <= :start_date)
                        group by scpr.contract_id, scpr.plot_id
                    ) as t2 on (t2.contract_id = c.id and t2.plot_id = kvs.gid)
				LEFT JOIN subleases_from_contracts as t1 on (t1.pc_rel_id = cpr.id)
                LEFT JOIN subleases_from_annexes as t3 on (t3.pc_rel_id = cpr.id)
 				WHERE (kvs.is_edited = FALSE AND (kvs.edit_active_from <= :start_date OR kvs.edit_active_from IS NULL))
				AND (pf.farming_id <> c.farming_id OR pf.farming_id is null)
				AND a.id is null
				AND c.nm_usage_rights <> '1'
				AND c.nm_usage_rights <> '4'
				AND t1.pc_rel_id is NULL
				AND t3.pc_rel_id is NULL
				AND CASE WHEN t2.total_area IS NOT NULL THEN t2.total_area > t2.sold_area ELSE c.id > 0 END
				AND c.is_annex = FALSE ";

        if ($options['start_date']) {
            $sql .= ' AND (c.start_date <= :due_date)';
        }

        if ($options['due_date']) {
            $sql .= ' AND (c.due_date IS NULL OR c.due_date >= :start_date)';
        }
        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['group'] && !$counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        $sql .= ') UNION (';
        $sql .= "SELECT {$return}
				FROM su_contracts C
                LEFT JOIN su_contracts a ON(a.parent_id = c.id AND a.active = true and a.start_date <= :due_date and a.due_date >= :start_date)
                LEFT JOIN su_contracts c_a ON (c_a.id = c.parent_id)
				INNER JOIN su_contracts_plots_rel cpr ON(cpr.contract_id = (CASE WHEN a.id IS NULL THEN c.id ELSE a.id END))
				INNER JOIN {$this->tableContracts} c1 ON(c1.id = cpr.contract_id)
				LEFT JOIN su_contracts_plots_rel c_a_rel ON (c_a_rel.contract_id = c_a.id and c_a_rel.plot_id = cpr.plot_id)
				LEFT JOIN su_subleases_plots_contracts_rel c_a_sub_plot_rel ON (c_a_sub_plot_rel.pc_rel_id = c_a_rel.id)
				LEFT JOIN su_contracts c_a_sub_c ON (c_a_sub_c.id = c_a_sub_plot_rel.sublease_id and c_a_sub_c.start_date <= :due_date and c_a_sub_c.due_date >= :start_date)
				LEFT JOIN layer_kvs kvs ON (cpr.plot_id = kvs.gid)
				LEFT JOIN su_plots_farming_rel pf ON(pf.pc_rel_id = cpr.id)
				LEFT JOIN su_plots_owners_rel po ON(po.pc_rel_id = cpr.id)
				LEFT JOIN su_owners o ON(o.id = po.owner_id)
                LEFT JOIN
                    (select scpr.contract_id, scpr.plot_id, SUM(scpr.contract_area_for_sale) as sold_area, MAX(scpr.contract_area) as total_area
                        FROM su_sales_contracts_plots_rel scpr
                        INNER join su_sales_contracts sc on(sc.id = scpr.sales_contract_id and sc.start_date <= :start_date)
                        group by scpr.contract_id, scpr.plot_id
                    ) as t2 on (t2.contract_id = c.id and t2.plot_id = kvs.gid)
                LEFT JOIN subleases_from_contracts as t1 on (t1.pc_rel_id = cpr.id)
                LEFT JOIN subleases_from_annexes as t3 on (t3.pc_rel_id = cpr.id)
				WHERE (kvs.is_edited = TRUE AND kvs.edit_active_from >= :due_date)
				AND (pf.farming_id <> c.farming_id OR pf.farming_id is null)
				AND c.nm_usage_rights <> '1'
				AND c.nm_usage_rights <> '4'
                AND case when t2.total_area is not null then t2.total_area > t2.sold_area else c.id > 0 end
				AND t1.pc_rel_id is NULL
				AND t3.pc_rel_id is NULL
				AND c.is_annex = FALSE ";

        if ($options['start_date']) {
            $sql .= ' AND (c.start_date <= :due_date OR a.start_date <= :due_date)';
        }

        if ($options['due_date']) {
            $sql .= ' AND (c.due_date IS NULL OR c.due_date >= :start_date OR a.due_date >= :start_date)';
        }
        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['group'] && !$counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        $sql .= ')';
        // END Getting plots from contracts

        // Getting plots from annexes
        $sql .= "UNION ( /* --getting plots from annexes */ SELECT {$return}
				FROM su_contracts C
				INNER JOIN su_contracts_plots_rel cpr ON(cpr.contract_id = c.id)
				INNER JOIN {$this->tableContracts} c1 ON(c1.id = cpr.contract_id)
				LEFT JOIN layer_kvs kvs ON (cpr.plot_id = kvs.gid)
				LEFT JOIN su_contracts a ON (a.parent_id = c.ID AND a.is_annex = true AND a.active = true AND (a.start_date >= c.start_date OR a.due_date <= c.due_date))
                LEFT JOIN su_contracts c_a ON (c_a.id = c.parent_id)
                LEFT JOIN su_contracts_plots_rel c_a_rel ON (c_a_rel.contract_id = c_a.id and c_a_rel.plot_id = cpr.plot_id)
				LEFT JOIN su_subleases_plots_contracts_rel c_a_sub_plot_rel ON (c_a_sub_plot_rel.pc_rel_id = c_a_rel.id)
				LEFT JOIN su_contracts c_a_sub_c ON (c_a_sub_c.id = c_a_sub_plot_rel.sublease_id and c_a_sub_c.start_date <= :due_date and c_a_sub_c.due_date >= :start_date)
				LEFT JOIN su_plots_farming_rel PF ON(pf.pc_rel_id = cpr.id)
				LEFT JOIN su_plots_owners_rel PO ON(po.pc_rel_id = cpr.id)
				LEFT JOIN su_owners O ON(o.id = po.owner_id)
                LEFT JOIN
                    (select scpr.contract_id, scpr.plot_id, SUM(scpr.contract_area_for_sale) as sold_area, MAX(scpr.contract_area) as total_area
                        FROM su_sales_contracts_plots_rel scpr
                        INNER join su_sales_contracts sc on(sc.id = scpr.sales_contract_id and sc.start_date <= :start_date)
                        group by scpr.contract_id, scpr.plot_id
                    ) as t2 on (t2.contract_id = c.id and t2.plot_id = kvs.gid)
				LEFT JOIN subleases_from_contracts as t1 on (t1.pc_rel_id = cpr.id)
                LEFT JOIN subleases_from_annexes as t3 on (t3.pc_rel_id = cpr.id)
 				WHERE (kvs.is_edited = FALSE AND (kvs.edit_active_from <= :start_date OR kvs.edit_active_from IS NULL))
				AND (pf.farming_id <> c.farming_id OR pf.farming_id is null)
				AND c.nm_usage_rights <> '1'
				AND c.nm_usage_rights <> '4'
				AND t1.pc_rel_id is NULL
				AND t3.pc_rel_id is NULL
				AND CASE WHEN t2.total_area IS NOT NULL THEN t2.total_area > t2.sold_area ELSE c.id > 0 END
				AND c.is_annex = TRUE ";

        if ($options['start_date']) {
            $sql .= ' AND (c.start_date <= :due_date)';
        }

        if ($options['due_date']) {
            $sql .= ' AND (c.due_date IS NULL OR c.due_date >= :start_date)';
        }
        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['group'] && !$counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        $sql .= ') UNION ( /* --getting plots from annexes */';
        $sql .= "SELECT {$return}
				FROM su_contracts C
				INNER JOIN su_contracts_plots_rel cpr ON(cpr.contract_id = c.id)
				INNER JOIN {$this->tableContracts} c1 ON(c1.id = cpr.contract_id)
				LEFT JOIN layer_kvs kvs ON (cpr.plot_id = kvs.gid)
				LEFT JOIN su_contracts a ON (a.parent_id = c.ID AND a.is_annex = true AND a.active = true AND (a.start_date >= c.start_date OR a.due_date <= c.due_date))
                LEFT JOIN su_contracts c_a ON (c_a.id = c.parent_id)
                LEFT JOIN su_contracts_plots_rel c_a_rel ON (c_a_rel.contract_id = c_a.id and c_a_rel.plot_id = cpr.plot_id)
				LEFT JOIN su_subleases_plots_contracts_rel c_a_sub_plot_rel ON (c_a_sub_plot_rel.pc_rel_id = c_a_rel.id)
				LEFT JOIN su_contracts c_a_sub_c ON (c_a_sub_c.id = c_a_sub_plot_rel.sublease_id and c_a_sub_c.start_date <= :due_date and c_a_sub_c.due_date >= :start_date)
				LEFT JOIN su_plots_farming_rel pf ON(pf.pc_rel_id = cpr.id)
				LEFT JOIN su_plots_owners_rel po ON(po.pc_rel_id = cpr.id)
				LEFT JOIN su_owners o ON(o.id = po.owner_id)
                LEFT JOIN
                    (select scpr.contract_id, scpr.plot_id, SUM(scpr.contract_area_for_sale) as sold_area, MAX(scpr.contract_area) as total_area
                        FROM su_sales_contracts_plots_rel scpr
                        INNER join su_sales_contracts sc on(sc.id = scpr.sales_contract_id and sc.start_date <= :start_date)
                        group by scpr.contract_id, scpr.plot_id
                    ) as t2 on (t2.contract_id = c.id and t2.plot_id = kvs.gid)
                LEFT JOIN subleases_from_contracts as t1 on (t1.pc_rel_id = cpr.id)
                LEFT JOIN subleases_from_annexes as t3 on (t3.pc_rel_id = cpr.id)
				WHERE (kvs.is_edited = TRUE AND kvs.edit_active_from >= :due_date)
				AND (pf.farming_id <> c.farming_id OR pf.farming_id is null)
				AND c.nm_usage_rights <> '1'
				AND c.nm_usage_rights <> '4'
                AND case when t2.total_area is not null then t2.total_area > t2.sold_area else c.id > 0 end
				AND t1.pc_rel_id is NULL
				AND t3.pc_rel_id is NULL
				AND c.is_annex = TRUE ";

        if ($options['start_date']) {
            $sql .= ' AND (c.start_date <= :due_date)';
        }

        if ($options['due_date']) {
            $sql .= ' AND (c.due_date IS NULL OR c.due_date >= :start_date)';
        }
        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['group'] && !$counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        $sql .= ')';

        // End Getting plots from annexes

        if ($options['include_subleases']) {
            $sql .= " UNION 
			( /* -- include_subleases */
				SELECT {$return}
						FROM
							{$this->tableKVS} kvs
							INNER JOIN {$this->contractsPlotsRelTable} cpr ON(cpr.plot_id = kvs.gid)
							INNER JOIN {$this->tableSubleasesPlotsContractsRel} spc ON(spc.pc_rel_id = cpr.id)
							INNER JOIN {$this->tableContracts} c ON(c.id = spc.sublease_id)
							INNER JOIN {$this->tableContracts} c1 ON(c1.id = cpr.contract_id)
							LEFT JOIN {$this->tableContractsContragents} cc ON(cc.contract_id = spc.sublease_id)
							LEFT JOIN {$this->plotsOwnersRelTable} po ON(po.pc_rel_id = cpr.id)
							LEFT JOIN {$this->tableOwners} o ON(o.id = po.owner_id)
							LEFT JOIN {$this->tableContracts} a on(a.parent_id = c1.id AND a.active = true and a.start_date <= :due_date and a.due_date >= :start_date)
							LEFT JOIN su_contracts c_a ON (c_a.id = c.parent_id)
							LEFT JOIN su_contracts_plots_rel c_a_rel ON (c_a_rel.contract_id = c_a.id and c_a_rel.plot_id = cpr.plot_id)
							LEFT JOIN su_subleases_plots_contracts_rel c_a_sub_plot_rel ON (c_a_sub_plot_rel.pc_rel_id = c_a_rel.id)
							LEFT JOIN su_contracts c_a_sub_c ON (c_a_sub_c.id = c_a_sub_plot_rel.sublease_id and c_a_sub_c.start_date <= :due_date and c_a_sub_c.due_date >= :start_date)
							LEFT JOIN su_plots_farming_rel pf ON(pf.pc_rel_id = cpr.id)
						WHERE (kvs.is_edited = FALSE AND (kvs.edit_active_from <= :start_date OR kvs.edit_active_from IS NULL))
							AND C .is_sublease = TRUE
							AND c1.nm_usage_rights <> '1'
							AND c.nm_usage_rights <> '4'
							AND a.id is NULL
							AND c.start_date <= :due_date
							AND c.due_date >= :start_date
							";
            if ($options['where']) {
                $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
            }
            if ($options['group'] && !$counter) {
                $sql .= ' GROUP BY ' . $options['group'];
            }
            $sql .= ") UNION
				(
				SELECT {$return}
						FROM
							{$this->tableKVS} kvs
							INNER JOIN {$this->contractsPlotsRelTable} cpr ON(cpr.plot_id = kvs.gid)
							INNER JOIN {$this->tableSubleasesPlotsContractsRel} spc ON(spc.pc_rel_id = cpr.id)
							INNER JOIN {$this->tableContracts} c ON(c.id = spc.sublease_id)
							INNER JOIN {$this->tableContracts} c1 ON(c1.id = cpr.contract_id)
							LEFT JOIN {$this->tableContractsContragents} cc ON(cc.contract_id = spc.sublease_id)
							LEFT JOIN {$this->plotsOwnersRelTable} po ON(po.pc_rel_id = cpr.id)
							LEFT JOIN {$this->tableOwners} o ON(o.id = po.owner_id)
							LEFT JOIN {$this->tableContracts} a on(a.parent_id = c1.id)
							LEFT JOIN su_contracts c_a ON (c_a.id = c.parent_id)
							LEFT JOIN su_contracts_plots_rel c_a_rel ON (c_a_rel.contract_id = c_a.id and c_a_rel.plot_id = cpr.plot_id)
							LEFT JOIN su_subleases_plots_contracts_rel c_a_sub_plot_rel ON (c_a_sub_plot_rel.pc_rel_id = c_a_rel.id)
							LEFT JOIN su_contracts c_a_sub_c ON (c_a_sub_c.id = c_a_sub_plot_rel.sublease_id and c_a_sub_c.start_date <= :due_date and c_a_sub_c.due_date >= :start_date)
							LEFT JOIN su_plots_farming_rel pf ON(pf.pc_rel_id = cpr.id)
						WHERE (kvs.is_edited = TRUE AND kvs.edit_active_from >= :due_date)
							AND C .is_sublease = TRUE
							AND c1.nm_usage_rights <> '1'
							AND c1.nm_usage_rights <> '4'
							and c.start_date <= :due_date
							And c.due_date >= :start_date";
            if ($options['where']) {
                $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
            }
            if ($options['group'] && !$counter) {
                $sql .= ' GROUP BY ' . $options['group'];
            }
            $sql .= ')';
        }

        if (!$counter && $options['sort']) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($options['extent']) {
            $sql .= ') data';
        }

        if ($counter) {
            $sql .= ') data';
        }

        if ($returnOnlySQL) {
            if ($options['due_date']) {
                $sql = str_replace(':due_date', "'" . $options['due_date'] . "'", $sql);
            }
            if ($options['start_date']) {
                $sql = str_replace(':start_date', "'" . $options['start_date'] . "'", $sql);
            }
            if ($options['where']['farming_id']) {
                $sql = str_replace(':farming_id', "'" . $options['where']['farming_id']['value'] . "'", $sql);
            }

            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']['ekate']) {
            $cmd->bindParameter(':ekate', $options['where']['ekate']['value']);
        }
        if ($options['start_date']) {
            $cmd->bindParameter(':start_date', $options['start_date']);
        }
        if ($options['due_date']) {
            $cmd->bindParameter(':due_date', $options['due_date']);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getOwnPlotsForReport($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);
        $sql = '';
        // With UNION to take correct "разделени или обединени имоти в справките" according to "edit_active_from"
        if ($counter) {
            $sql = 'SELECT sum(count) AS count FROM (';
        }
        if ($options['extent']) {
            $sql = "SELECT {$options['extent']} FROM (";
        }
        $sql .= "(
			( SELECT {$return}
			FROM
				{$this->contractsPlotsRelTable} cpr
			LEFT JOIN {$this->tableContracts} C ON (C . ID = cpr.contract_id)
			LEFT JOIN {$this->tableContracts} a on(a.parent_id = c.id AND a.active = true)
			LEFT JOIN {$this->tableKVS} kvs ON (kvs.gid = cpr.plot_id)
			LEFT JOIN
				(select scpr.contract_id, scpr.plot_id, SUM(scpr.contract_area_for_sale) as sold_area, MAX(scpr.contract_area) as total_area
					FROM su_sales_contracts_plots_rel scpr
					INNER join su_sales_contracts sc on(sc.id = scpr.sales_contract_id and sc.start_date <= :start_date)
					group by scpr.contract_id, scpr.plot_id
				) as t1 on (t1.contract_id = c.id and t1.plot_id = kvs.gid)
			WHERE (kvs.is_edited = FALSE AND (kvs.edit_active_from <= :due_date OR kvs.edit_active_from IS NULL))
			AND case when t1.total_area is not null then t1.total_area > t1.sold_area else c.id > 0 end
			AND C.nm_usage_rights = 1
			AND C.start_date <= :start_date
			AND C.is_annex = 'false'";

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }
        if ($options['group'] && !$counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        $sql .= ")) UNION (( SELECT {$return}
			FROM
				{$this->contractsPlotsRelTable} cpr
			LEFT JOIN {$this->tableContracts} C ON (C . ID = cpr.contract_id)
			LEFT JOIN {$this->tableContracts} a on(a.parent_id = c.id AND a.active = true)
			LEFT JOIN {$this->tableKVS} kvs ON (kvs.gid = cpr.plot_id)
            LEFT JOIN
				(select scpr.contract_id, scpr.plot_id, SUM(scpr.contract_area_for_sale) as sold_area, MAX(scpr.contract_area) as total_area
					FROM su_sales_contracts_plots_rel scpr
					INNER join su_sales_contracts sc on(sc.id = scpr.sales_contract_id and sc.start_date <= :start_date)
					group by scpr.contract_id, scpr.plot_id
				) as t1 on (t1.contract_id = c.id and t1.plot_id = kvs.gid)
			WHERE (kvs.is_edited = TRUE AND kvs.edit_active_from >= :due_date)
			AND case when t1.total_area is not null then t1.total_area > t1.sold_area else c.id > 0 end
			AND C .nm_usage_rights = 1
			AND C .start_date <= :start_date
			AND C .is_annex = 'false'";

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }
        if ($options['group'] && !$counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        $sql .= '))';

        if (!$counter && $options['sort']) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($options['extent']) {
            $sql .= ') data';
        }

        if ($counter) {
            $sql .= ') data';
        }

        if ($returnOnlySQL) {
            $sql = str_replace(':start_date', "'" . $options['start_date'] . "'", $sql);

            return str_replace(':due_date', "'" . $options['due_date'] . "'", $sql);
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        $cmd->bindParameter(':start_date', $options['start_date']);
        $cmd->bindParameter(':due_date', $options['due_date']);

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getUsedPlotsForReport($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $sql = 'SELECT * FROM (';
        // With UNION to take correct "разделени или обединени имоти в справките" according to "edit_active_from"
        if ($counter) {
            $sql = 'SELECT sum(count) AS count FROM (';
        }
        if ($options['extent']) {
            $sql = "SELECT {$options['extent']} FROM (";
        }
        $sql .= '(
            WITH subleased_contract_annex as (
                SELECT
                    a.c_num,
                    a.start_date start_date,
                    a.due_date due_date,
                    a.c_num,
                    a.id a_id,
                    cpr.id cpr_id,
                    cpr2.id a_cpr_id,
                    spcr.sublease_id,
                    COALESCE (cpr2.id, cpr.id) pc_rel_id,
                    sub.id AS sub_id
                FROM
                    su_contracts a
                LEFT JOIN su_contracts_plots_rel cpr ON cpr.contract_id = a.id
                LEFT JOIN su_subleases_plots_contracts_rel spcr ON spcr.pc_rel_id = cpr.id
                LEFT JOIN su_contracts sub ON (
                    sub.id = spcr.sublease_id
                    AND sub.is_sublease
                    AND sub.start_date <= :start_date
                    AND sub.due_date >= :due_date
                    AND sub.active = TRUE
                )
                LEFT JOIN su_contracts_plots_rel cpr2 ON cpr2.contract_id = a.id AND cpr.plot_id = cpr2.plot_id
                WHERE
                    a.is_annex = TRUE
                    AND a.is_sublease = FALSE
                    AND a.nm_usage_rights <> 4
                    AND spcr.sublease_id NOTNULL
                    AND sub.id NOTNULL
                    AND (
                        a.start_date <= :start_date
                        AND (a.due_date >= :due_date OR a.due_date ISNULL)
                    )
            ),
            subleased_contracts AS (
                SELECT
                    sc.id
                    , sc.c_num
                    , sc.nm_usage_rights
                    , scpr.plot_id
                    , sspcr.sublease_id
                    , sub.c_num
                    , scpr.id pc_rel_id
                FROM
                    su_contracts sc
                INNER JOIN su_contracts_plots_rel scpr ON
                    scpr.contract_id = sc.id
                INNER JOIN su_subleases_plots_contracts_rel sspcr ON
                    sspcr.pc_rel_id = scpr.id
                LEFT JOIN su_contracts sub ON
                    sub.active = true and sub.id = sspcr.sublease_id
                WHERE
                    sc.active
                    AND sc.is_sublease = FALSE
                    AND (
                        sc.start_date <= :start_date
                        AND (sc.due_date >= :due_date OR sc.due_date ISNULL)
                    )
                    AND sc.is_annex = FALSE
                    AND sc.nm_usage_rights <> 4
                    AND ( sub.start_date <= :start_date
                    AND sub.due_date >= :due_date)
            )';

        $annexesDateFilter = '';
        if ($options['start_date']) {
            $annexesDateFilter .= ' AND a.start_date <= :due_date ';
        }

        if ($options['start_date']) {
            $annexesDateFilter .= ' AND a.due_date >= :due_date ';
        }
        $sql .= "( SELECT
                {$return},
                c.c_num,
                c.id as c_id
            FROM su_contracts C
            INNER JOIN su_contracts_plots_rel cpr ON cpr.contract_id = c.id
            INNER JOIN su_contracts c1 on (c1.id = cpr.contract_id)
            LEFT JOIN layer_kvs kvs ON (cpr.plot_id = kvs.gid)
            LEFT JOIN su_plots_farming_rel PF ON(pf.pc_rel_id = cpr.id)
            LEFT JOIN su_plots_owners_rel PO ON(po.pc_rel_id = cpr.id)
            LEFT JOIN su_contracts a ON(c.id = a.parent_id {$annexesDateFilter})
            LEFT JOIN su_contracts c_a ON (c_a.id = c.parent_id)
			LEFT JOIN su_contracts_plots_rel c_a_rel ON (c_a_rel.contract_id = c_a.id and c_a_rel.plot_id = cpr.plot_id)
			LEFT JOIN su_subleases_plots_contracts_rel c_a_sub_plot_rel ON (c_a_sub_plot_rel.pc_rel_id = c_a_rel.id)
			LEFT JOIN su_contracts c_a_sub_c ON (c_a_sub_c.id = c_a_sub_plot_rel.sublease_id and c_a_sub_c.start_date <= :due_date and c_a_sub_c.due_date >= :due_date)
            LEFT JOIN su_owners O ON(o.id = po.owner_id)
            LEFT JOIN
                (select scpr.contract_id, scpr.plot_id, SUM(scpr.contract_area_for_sale) as sold_area, MAX(scpr.contract_area) as total_area
                    FROM su_sales_contracts_plots_rel scpr
                    INNER join su_sales_contracts sc on(sc.id = scpr.sales_contract_id and sc.start_date <= :start_date)
                    group by scpr.contract_id, scpr.plot_id
                ) as t2 on (t2.contract_id = c.id and t2.plot_id = kvs.gid)
            LEFT JOIN subleased_contract_annex as t1 on (t1.pc_rel_id = cpr.id)
            LEFT JOIN subleased_contracts t3 ON (t3.pc_rel_id = cpr.id)
            WHERE (kvs.is_edited = FALSE AND (kvs.edit_active_from <= :due_date OR kvs.edit_active_from IS NULL))
            AND case when t2.total_area is not null then t2.total_area > t2.sold_area else c.id > 0 end
            AND t1.pc_rel_id is NULL
            AND t3.pc_rel_id is NULL
            AND a.id is null
            AND c.nm_usage_rights <> '4'
            AND c.is_sublease = false
            AND (
                CASE WHEN c.nm_usage_rights = 1 THEN TRUE ELSE pf.farming_id <> C .farming_id END
                OR pf.farming_id IS NULL
            )
            AND c.is_annex = false
            AND cpr.annex_action = 'added'
            AND c.active = true
        ";
        if ($options['start_date']) {
            $sql .= ' AND (c.start_date <= :start_date)';
        }
        if ($options['due_date']) {
            $sql .= ' AND (c.due_date IS NULL OR c.due_date >= :due_date)';
        }
        if ($options['group'] && !$counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }
        $sql .= ')';
        // Start getting annexes
        $sql .= "  -- Start getting annexes 
            UNION (
                  SELECT
                  {$return},
                  c.c_num,
                c.id as c_id
            FROM su_contracts C
            LEFT JOIN su_contracts a ON(a.parent_id = c.id AND a.active = true and a.start_date <= :due_date and a.due_date >= :start_date)
            LEFT JOIN su_contracts c_a ON (c_a.id = c.parent_id)
            INNER JOIN su_contracts_plots_rel cpr ON cpr.contract_id = c.id
            INNER JOIN su_contracts c1 on (c1.id = cpr.contract_id)
            LEFT JOIN su_contracts_plots_rel c_a_rel ON (c_a_rel.contract_id = c_a.id and c_a_rel.plot_id = cpr.plot_id)
			LEFT JOIN su_subleases_plots_contracts_rel c_a_sub_plot_rel ON (c_a_sub_plot_rel.pc_rel_id = c_a_rel.id)
			LEFT JOIN su_contracts c_a_sub_c ON (c_a_sub_c.id = c_a_sub_plot_rel.sublease_id and c_a_sub_c.start_date <= :due_date and c_a_sub_c.due_date >= :due_date)
            LEFT JOIN layer_kvs kvs ON (cpr.plot_id = kvs.gid)
            LEFT JOIN su_plots_farming_rel PF ON(pf.pc_rel_id = cpr.id)
            LEFT JOIN su_plots_owners_rel PO ON(po.pc_rel_id = cpr.id)
            LEFT JOIN su_owners O ON(o.id = po.owner_id)
            LEFT JOIN
                (select scpr.contract_id, scpr.plot_id, SUM(scpr.contract_area_for_sale) as sold_area, MAX(scpr.contract_area) as total_area
                    FROM su_sales_contracts_plots_rel scpr
                    INNER join su_sales_contracts sc on(sc.id = scpr.sales_contract_id and sc.start_date <= :start_date)
                    group by scpr.contract_id, scpr.plot_id
                ) as t2 on (t2.contract_id = c.id and t2.plot_id = kvs.gid)
            LEFT JOIN subleased_contract_annex as t1 on (t1.pc_rel_id = cpr.id)
            LEFT JOIN subleased_contracts t3 ON (t3.pc_rel_id = cpr.id)
            WHERE (kvs.is_edited = FALSE AND (kvs.edit_active_from <= :due_date OR kvs.edit_active_from IS NULL))
            AND case when t2.total_area is not null then t2.total_area > t2.sold_area else c.id > 0 end
            AND t1.pc_rel_id is NULL
            AND t3.pc_rel_id is NULL
            AND c.nm_usage_rights <> '4'
            AND c.is_sublease = false
            AND (
                CASE WHEN c.nm_usage_rights = 1 THEN TRUE ELSE pf.farming_id <> C .farming_id END
                OR pf.farming_id IS NULL
            )
            AND c.is_annex = true
            AND cpr.annex_action = 'added'
            AND c.active = true
				";
        if ($options['start_date']) {
            $sql .= ' AND (c.start_date <= :start_date)';
        }

        if ($options['due_date']) {
            $sql .= ' AND (c.due_date IS NULL OR c.due_date >= :due_date)';
        }
        if ($options['group'] && !$counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }
        $sql .= ')';
        // End getting annexes
        $sql .= " -- End getting annexes
            UNION (
                  SELECT
                  {$return},
                  case when c.is_sublease then c1.c_num else c.c_num end as c_num,
                  case when c.is_sublease then c1.id else c.id end as c_id
                    FROM su_contracts C
                    INNER JOIN su_contracts_plots_rel cpr ON cpr.contract_id = c.id
                    INNER JOIN {$this->tableContracts} c1 ON(c1.id = cpr.contract_id)
                    LEFT JOIN layer_kvs kvs ON (cpr.plot_id = kvs.gid)
                    LEFT JOIN su_contracts a ON (a.parent_id = c.ID AND a.is_annex = true AND (a.start_date >= c.start_date OR a.due_date <= c.due_date))
                    LEFT JOIN su_contracts c_a ON (c_a.id = c.parent_id)
                    LEFT JOIN su_contracts_plots_rel c_a_rel ON (c_a_rel.contract_id = c_a.id and c_a_rel.plot_id = cpr.plot_id)
				    LEFT JOIN su_subleases_plots_contracts_rel c_a_sub_plot_rel ON (c_a_sub_plot_rel.pc_rel_id = c_a_rel.id)
				    LEFT JOIN su_contracts c_a_sub_c ON (c_a_sub_c.id = c_a_sub_plot_rel.sublease_id and c_a_sub_c.start_date <= :due_date and c_a_sub_c.due_date >= :due_date)
                    LEFT JOIN su_plots_farming_rel PF ON(pf.pc_rel_id = cpr.id)
                    LEFT JOIN su_plots_owners_rel PO ON(po.pc_rel_id = cpr.id)
                    LEFT JOIN su_owners O ON(o.id = po.owner_id)
                    LEFT JOIN
                        (select scpr.contract_id, scpr.plot_id, SUM(scpr.contract_area_for_sale) as sold_area, MAX(scpr.contract_area) as total_area
                            FROM su_sales_contracts_plots_rel scpr
                            INNER join su_sales_contracts sc on(sc.id = scpr.sales_contract_id and sc.start_date <= :start_date)
                            group by scpr.contract_id, scpr.plot_id
                        ) as t2 on (t2.contract_id = c.id and t2.plot_id = kvs.gid)
                    LEFT JOIN
                        (
							SELECT
								C .c_num,
								COALESCE (A .start_date, C .start_date) start_date,
								COALESCE (A .due_date, C .due_date) due_date,
								C .start_date,
								A .c_num,
								A . ID a_id,
								cpr. ID cpr_id,
								cpr2. ID a_cpr_id,
								spcr.sublease_id,
								COALESCE (cpr2. ID, cpr. ID) pc_rel_id,
								sub.id AS sub_id
							FROM
								su_contracts C
							LEFT JOIN su_contracts A ON (
								A .parent_id = C . ID
								AND C .is_annex = FALSE
								AND C .is_sublease = FALSE
								AND C .nm_usage_rights <> 4
								AND A .is_annex = TRUE
								AND A .start_date <= :start_date
								AND A .due_date >= :due_date
							)
							LEFT JOIN su_contracts_plots_rel cpr ON cpr.contract_id = C . ID
							LEFT JOIN su_subleases_plots_contracts_rel spcr ON spcr.pc_rel_id = cpr. ID
							LEFT JOIN su_contracts sub ON (
								sub.id = spcr.sublease_id
								AND sub.is_sublease
								AND sub.start_date <= :start_date
                                AND sub.due_date >= :due_date
                                AND sub.active = TRUE
							)
							LEFT JOIN su_contracts_plots_rel cpr2 ON cpr2.contract_id = A . ID
							AND cpr.plot_id = cpr2.plot_id
							WHERE
								C .is_annex = FALSE
							AND C .is_sublease = FALSE
							AND C .nm_usage_rights <> 4
							AND spcr.sublease_id NOTNULL
							AND sub.id NOTNULL
							AND (
								C .start_date <= :start_date
								AND (C .due_date >= :due_date OR C .due_date ISNULL)
							)
                        ) as t1 on (t1.pc_rel_id = cpr.id)
                    WHERE (kvs.is_edited = TRUE AND kvs.edit_active_from >= :due_date)
                    AND case when t2.total_area is not null then t2.total_area > t2.sold_area else c.id > 0 end
                    AND t1.pc_rel_id is NULL
                    AND c.nm_usage_rights <> '4'
                    AND c.is_sublease = false
                    AND (
                        CASE WHEN c.nm_usage_rights = 1 THEN TRUE ELSE pf.farming_id <> C .farming_id END
                        OR pf.farming_id IS NULL
                    )
                    AND c.is_annex = false
                    AND cpr.annex_action = 'added'
                    AND c.active = true
				";
        if ($options['start_date']) {
            $sql .= ' AND (c.start_date <= :start_date)';
        }

        if ($options['due_date']) {
            $sql .= ' AND (c.due_date IS NULL OR c.due_date >= :due_date)';
        }
        if ($options['group'] && !$counter) {
            $sql .= ' GROUP BY ' . $options['group'] . ', c1.id';
        }
        $sql .= ')';

        if ($options['include_subleases']) {
            $sql .= " UNION (
                SELECT
                {$return},
                case when c.is_sublease then c1.c_num else c.c_num end as c_num,
                case when c.is_sublease then c1.id else c.id end as c_id
                FROM
                    {$this->tableKVS} kvs
                    INNER JOIN {$this->contractsPlotsRelTable} cpr ON(cpr.plot_id = kvs.gid)
                    INNER JOIN {$this->tableSubleasesPlotsContractsRel} spc ON(spc.pc_rel_id = cpr.id)
                    INNER JOIN {$this->tableContracts} c ON(c.id = spc.sublease_id)
                    INNER JOIN {$this->tableContracts} c1 ON(c1.id = cpr.contract_id)
                    LEFT JOIN {$this->tableContractsContragents} cc ON(cc.contract_id = spc.sublease_id)
                    LEFT JOIN {$this->plotsOwnersRelTable} po ON(po.pc_rel_id = cpr.id)
                    LEFT JOIN {$this->tableOwners} o ON(o.id = po.owner_id)
                    LEFT JOIN
                        (select scpr.contract_id, scpr.plot_id, SUM(scpr.contract_area_for_sale) as sold_area, MAX(scpr.contract_area) as total_area
                            FROM su_sales_contracts_plots_rel scpr
                            INNER join su_sales_contracts sc on(sc.id = scpr.sales_contract_id and sc.start_date <= :start_date)
                            group by scpr.contract_id, scpr.plot_id
                        ) as t2 on (t2.contract_id = c.id and t2.plot_id = kvs.gid)
                    LEFT JOIN {$this->tableContracts} a on(a.parent_id = c1.id AND a.active = true AND a.is_annex = TRUE  and a.start_date <= :due_date and a.due_date >= :start_date)
                    LEFT JOIN su_contracts c_a ON (c_a.id = c.parent_id)
					LEFT JOIN su_contracts_plots_rel c_a_rel ON (c_a_rel.contract_id = c_a.id and c_a_rel.plot_id = cpr.plot_id)
					LEFT JOIN su_subleases_plots_contracts_rel c_a_sub_plot_rel ON (c_a_sub_plot_rel.pc_rel_id = c_a_rel.id)
					LEFT JOIN su_contracts c_a_sub_c ON (c_a_sub_c.id = c_a_sub_plot_rel.sublease_id and c_a_sub_c.start_date <= :due_date and c_a_sub_c.due_date >= :due_date)
                    LEFT JOIN su_plots_farming_rel pf ON(pf.pc_rel_id = cpr.id)
                WHERE (kvs.is_edited = FALSE AND (kvs.edit_active_from <= :due_date OR kvs.edit_active_from IS NULL))
                    AND case when t2.total_area is not null then t2.total_area > t2.sold_area else c.id > 0 end
                    AND C .is_sublease = TRUE
                    AND c1.nm_usage_rights <> '4'
                    and c.start_date <= :start_date
                    And c.due_date >= :due_date
                    AND c1.active = 'true'
                    AND c.is_annex = false
                    AND a.id is NULL
                    AND cpr.annex_action = 'added'
                    AND c.active = true
                    ";
            if ($options['group'] && !$counter) {
                $sql .= ' GROUP BY ' . $options['group'] . ', c1.ID';
            }
            $sql .= ") UNION
				(
                SELECT
                {$return},
                case when c.is_sublease then c1.c_num else c.c_num end as c_num,
                case when c.is_sublease then c1.id else c.id end as c_id
                FROM
                    {$this->tableKVS} kvs
                    INNER JOIN {$this->contractsPlotsRelTable} cpr ON(cpr.plot_id = kvs.gid)
                    INNER JOIN {$this->tableSubleasesPlotsContractsRel} spc ON(spc.pc_rel_id = cpr.id)
                    INNER JOIN {$this->tableContracts} c ON(c.id = spc.sublease_id)
                    INNER JOIN {$this->tableContracts} c1 ON(c1.id = cpr.contract_id)
                    LEFT JOIN {$this->tableContractsContragents} cc ON(cc.contract_id = spc.sublease_id)
                    LEFT JOIN {$this->plotsOwnersRelTable} po ON(po.pc_rel_id = cpr.id)
                    LEFT JOIN {$this->tableOwners} o ON(o.id = po.owner_id)
                    LEFT JOIN
                        (select scpr.contract_id, scpr.plot_id, SUM(scpr.contract_area_for_sale) as sold_area, MAX(scpr.contract_area) as total_area
                            FROM su_sales_contracts_plots_rel scpr
                            INNER join su_sales_contracts sc on(sc.id = scpr.sales_contract_id and sc.start_date <= :start_date)
                            group by scpr.contract_id, scpr.plot_id
                        ) as t2 on (t2.contract_id = c.id and t2.plot_id = kvs.gid)
                    LEFT JOIN {$this->tableContracts} a on(a.parent_id = c1.id AND a.active = true)
                    LEFT JOIN su_contracts c_a ON (c_a.id = c.parent_id)
					LEFT JOIN su_contracts_plots_rel c_a_rel ON (c_a_rel.contract_id = c_a.id and c_a_rel.plot_id = cpr.plot_id)
					LEFT JOIN su_subleases_plots_contracts_rel c_a_sub_plot_rel ON (c_a_sub_plot_rel.pc_rel_id = c_a_rel.id)
					LEFT JOIN su_contracts c_a_sub_c ON (c_a_sub_c.id = c_a_sub_plot_rel.sublease_id and c_a_sub_c.start_date <= :due_date and c_a_sub_c.due_date >= :due_date)
                    LEFT JOIN su_plots_farming_rel pf ON(pf.pc_rel_id = cpr.id)
                WHERE (kvs.is_edited = TRUE AND kvs.edit_active_from >= :due_date)
                    AND case when t2.total_area is not null then t2.total_area > t2.sold_area else c.id > 0 end
                    AND C .is_sublease = TRUE
                    AND c1.nm_usage_rights <> '4'
                    and c.start_date <= :start_date
                    And c.due_date >= :due_date
                    AND c1.active = 'true'
                    AND c.is_annex = false
                    AND cpr.annex_action = 'added'
                    AND c.active = true";
            if ($options['group'] && !$counter) {
                $sql .= ' GROUP BY ' . $options['group'] . ', c1.ID';
            }
            $sql .= ')';
        }

        $sql .= ')';
        $sql .= ') data';

        $sql .= ' WHERE true ';
        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if (!$counter && $options['sort']) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'] . ', c_id';
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            if ($options['due_date']) {
                $sql = str_replace(':due_date', "'" . $options['due_date'] . "'", $sql);
            }
            if ($options['start_date']) {
                $sql = str_replace(':start_date', "'" . $options['start_date'] . "'", $sql);
            }

            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']['ekate']) {
            $cmd->bindParameter(':ekate', $options['where']['ekate']['value']);
        }
        if ($options['start_date']) {
            $cmd->bindParameter(':start_date', $options['start_date'], PDO::PARAM_STR);
        }
        if ($options['due_date']) {
            $cmd->bindParameter(':due_date', $options['due_date'], PDO::PARAM_STR);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getContractDetailsForPlotForReport($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);
        $sql = '';
        // With UNION to take correct "разделени или обединени имоти в справките" according to "edit_active_from"
        if ($counter) {
            $sql = 'SELECT sum(count) AS count FROM (';
        }
        if ($options['extent']) {
            $sql = "SELECT {$options['extent']} FROM (";
        }
        $sql .= "( SELECT {$return}
			,C .nm_usage_rights, cpr.contract_area as contract_area
			FROM
				su_contracts_plots_rel cpr
			LEFT JOIN su_contracts C ON (cpr.contract_id = C . ID)
			LEFT JOIN layer_kvs kvs ON (cpr.plot_id = kvs.gid)
			LEFT JOIN su_subleases_plots_contracts_rel scpr ON (scpr.pc_rel_id = cpr.id)
			WHERE
				TRUE ";

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        $sql .= ") UNION
				( SELECT {$return}
					, 7 as nm_usage_rights, cpr.contract_area_for_sale as contract_area
					FROM
						su_sales_contracts_plots_rel cpr
					LEFT JOIN su_sales_contracts C ON (cpr.sales_contract_id = C . ID)
					LEFT JOIN layer_kvs kvs ON (cpr.plot_id = kvs.gid)
					WHERE
						TRUE";

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        $sql .= ") UNION
				( SELECT {$return}
				,C .nm_usage_rights, 
				coalesce(sspa.contract_area, cpr.contract_area) as contract_area
					FROM
					su_subleases_plots_contracts_rel scpr
				LEFT JOIN su_contracts_plots_rel cpr on (scpr.pc_rel_id = cpr.id)
				LEFT JOIN layer_kvs kvs ON (cpr.plot_id = kvs.gid)
				LEFT JOIN su_contracts c on (scpr.sublease_id = c.id)
				LEFT JOIN su_subleases_plots_area sspa on sspa.sublease_id = c.id and sspa.plot_id = cpr.plot_id
				WHERE
					TRUE";

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        $sql .= ')';

        if (!$counter && $options['sort']) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        if ($options['group'] && !$counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($options['extent']) {
            $sql .= ') data';
        }

        if ($counter) {
            $sql .= ') data';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getPlotOwnerDataByPlotID($plot_id)
    {
        $sql = "SELECT
		    	c.id AS contract_id,
		    	c.start_date AS c_start_date,
		    	sc.start_date AS sc_start_date,
		    	cpr. ID AS contract_plot_id,
		    	c.c_num,
		    	sc.c_num as s_c_num,
		    	cpr.plot_id,
		    	c.farming_id,
		    	nm_usage_rights,
		    	(CASE WHEN c.start_date <= sc.start_date THEN 7 ELSE 1 END) AS c_type,
		    	string_agg(b.name, ', ') AS buyer
		    FROM
		    	su_contracts_plots_rel cpr
		    LEFT JOIN su_contracts C ON cpr.contract_id = C . ID
		    LEFT JOIN su_sales_contracts_plots_rel scpr ON cpr.plot_id = scpr.plot_id
		    LEFT JOIN su_sales_contracts sc ON scpr.sales_contract_id = sc. ID
		    LEFT JOIN su_sales_contracts_buyers_rel scbr ON scbr.sales_contract_id = sc.id
		    LEFT JOIN su_buyers b ON scbr.buyer_id = b.id
		    WHERE
		    	C .nm_usage_rights = 1
		    AND cpr.plot_id = :plot_id
		    GROUP BY c.id,sc.id,cpr.id, scpr.id, b.id
		    ORDER by cpr. plot_id DESC
		    LIMIT 1
    	";

        $cmd = $this->DbModule->createCommand($sql);

        $cmd->bindParameter(':plot_id', $plot_id);

        return $cmd->query()->readAll();
    }

    public function getPlotDetailsFrom($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $sql = "
    	SELECT {$return}
			FROM
			{$this->tableKVS} kvs
			LEFT JOIN {$this->contractsPlotsRelTable} cpr ON (kvs.gid = cpr.plot_id)
			LEFT JOIN {$this->tableContracts} C ON (C . ID = cpr.contract_id)
		";

        if ($options['joins']) {
            foreach ($options['joins'] as $join) {
                $sql .= ' ' . $join . ' ';
            }
        }

        $sql .= ' WHERE TRUE ';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['group'] && !$counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if (!$counter && $options['sort']) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    /**
     * Return the thematic map info by id.
     *
     * @param int $mapID
     *
     * @return array
     *               {
     *               filters       - array of applied filters
     *               name          - thematic map name
     *               criteria      - the main coloring criteria
     *               criteria_text - the text of the criteria
     *               map_layer     - the main map layer
     *               colors        - the chosen colors array
     *               }
     */
    public function getThematicMapInfoById($mapID)
    {
        $sql = 'SELECT * FROM su_thematic_maps WHERE id = :map_id';
        $cmd = $this->DbModule->createCommand($sql);

        $cmd->bindParameter(':map_id', $mapID);

        return $cmd->query()->readAll();
    }

    public function getThematicMapInfoForKVSLayerWithContracts($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return}
                FROM {$this->tableContracts} C
                LEFT JOIN {$this->tableContracts} a ON (a.parent_id = c.id AND a.active = true and a.start_date <= :start_date and a.due_date >= :due_date )
                INNER JOIN {$this->contractsPlotsRelTable} cpr ON(cpr.contract_id = (CASE WHEN a.id IS NULL THEN c.id ELSE a.id END))
                LEFT JOIN {$this->tableKVS} kvs ON (cpr.plot_id = kvs.gid)
                LEFT JOIN {$this->plotsOwnersRelTable} por on (por.pc_rel_id = cpr.id  AND por.is_heritor = false)
                LEFT JOIN {$this->plotsFarmingRelTable} pfr on (pfr.pc_rel_id = cpr.id)
                LEFT JOIN {$this->tableOwners} o on (o.id = por.owner_id)
                LEFT JOIN
                    ( select spc.pc_rel_id from su_subleases_plots_contracts_rel as spc
                        join su_contracts as c2 on (c2.id = spc.sublease_id)
                        where
                          c2.due_date >= :due_date
                          and c2.start_date <= :start_date
                          and c2.is_sublease = true
                          and c2.active = true
                    ) as t1 on (t1.pc_rel_id = cpr.id)
        WHERE  TRUE ";

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);

            if ($options['t1.pc_rel_id']) {
                $sql .= ' AND t1.pc_rel_id is NULL ';
            } else {
                $sql .= ' AND t1.pc_rel_id is not NULL ';
            }
        }

        if ($options['group']) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($options['order'] && $options['sort'] && !$counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            $sql = str_replace(':start_date', "'" . $options['start_date'] . "'", $sql);

            return str_replace(':due_date', "'" . $options['due_date'] . "'", $sql);
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }
        if ($options['whereOr']) {
            $this->createWhereBinds($cmd, $options['whereOr']);
        }

        if ('' != $options['start_date']) {
            $cmd->bindValue(':start_date', $options['start_date']);
        }
        if ('' != $options['due_date']) {
            $cmd->bindValue(':due_date', $options['due_date']);
        }

        return $cmd->query()->readAll();
    }

    public function getThematicMapInfoForKVSLayer($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return}
                FROM {$this->tableKVS} kvs
                WHERE true";

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['group']) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($options['order'] && $options['sort'] && !$counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }
        if ($options['whereOr']) {
            $this->createWhereBinds($cmd, $options['whereOr']);
        }

        return $cmd->query()->readAll();
    }

    public function getSubleasedPlotGidsForPeriod($start_date, $due_date)
    {
        $sql = '
        SELECT
            array_agg(DISTINCT cpr.plot_id) AS plot_id
        FROM su_contracts_plots_rel cpr
        LEFT JOIN su_contracts c ON cpr.contract_id = c.id
        LEFT JOIN su_subleases_plots_contracts_rel spcr ON cpr.id = spcr.pc_rel_id
        LEFT JOIN su_contracts c1 ON c1.id = spcr.sublease_id
        WHERE c1.id IS NOT NULL
        AND c.active = true
        AND c1.active = true
        ';

        if ('' != $start_date) {
            $sql .= ' AND c1.start_date <= :due_date';
        }
        if ('' != $due_date) {
            $sql .= ' AND c.due_date >= :start_date';
        }

        $cmd = $this->DbModule->createCommand($sql);

        if ('' != $start_date) {
            $cmd->bindValue(':start_date', $start_date);
        }
        if ('' != $due_date) {
            $cmd->bindValue(':due_date', $due_date);
        }

        $cmd->execute();

        return $cmd->query()->readAll();
    }

    public function getSoldPlotsData($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter);
        $sql = "
        SELECT
			{$return}
		FROM
			su_sales_contracts sc
		JOIN su_sales_contracts_plots_rel scpr ON (
			sc.id = scpr.sales_contract_id
		)
        LEFT JOIN su_contracts_plots_rel cpr ON (scpr.contract_id = cpr.contract_id AND cpr.plot_id = scpr.plot_id)
		LEFT JOIN su_contracts c ON (c.id = cpr.contract_id)		
        WHERE
			TRUE ";

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['whereOr']) {
            $sql = $this->createWhereOrSQL($sql, $options['whereOr'], $returnOnlySQL);
        }

        if ($options['group']) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        if ($options['whereOr']) {
            $this->createWhereBinds($cmd, $options['whereOr']);
        }

        $cmd->execute();

        return $cmd->query()->readAll();
    }

    public function getDataForTotalAreaReport($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);
        $sql = '';
        // With UNION to take correct "разделени или обединени имоти в справките" according to "edit_active_from"
        if ($counter) {
            $sql = 'SELECT sum(count) AS count FROM (';
        }
        if ($options['extent']) {
            $sql = "SELECT {$options['extent']} FROM (";
        }
        $sql .= "
			( SELECT {$return}
                FROM su_contracts C
                left JOIN su_contracts a ON(a.parent_id = c.id AND a.active = true and a.start_date <= :start_date and a.due_date >= :due_date)
				INNER JOIN su_contracts_plots_rel cpr ON(cpr.contract_id = (CASE WHEN a.id IS NULL THEN c.id ELSE a.id END))
                LEFT JOIN {$this->tableKVS} kvs ON (cpr.plot_id = kvs.gid)
                LEFT JOIN {$this->plotsOwnersRelTable} po ON(po.pc_rel_id = cpr.id)
				LEFT JOIN {$this->tableOwners} o ON(o.id = po.owner_id)
				LEFT JOIN {$this->tableOwnersReps} o_r ON(o_r.id = po.rep_id)
        		LEFT JOIN {$this->viewTopicLayerByOwnerNameLabelItems} tkvs on (tkvs.gid = kvs.gid)
			WHERE (kvs.is_edited = FALSE AND (kvs.edit_active_from <= :due_date OR kvs.edit_active_from IS NULL)) ";
        if (!$options['include_subleases']) {
            $sql .= " AND cpr.id not in (
                select pc_rel_id from
                    {$this->tableSubleasesPlotsContractsRel} scpr1
                left join su_contracts c1 on (scpr1.sublease_id = c1.id)
                where c1.start_date <= :start_date
                and c1.due_date >= :due_date
                and c1.is_sublease = true
            )";
        }
        if ($options['exclude_sold_plots']) {
            $sql .= "AND cpr.plot_id NOT IN (
				SELECT
				plot_id
				FROM
				{$this->tableSalesContracts} sc
				LEFT JOIN {$this->salesContractsPlotsRelTable} scpr ON (sc. ID = scpr.sales_contract_id)
				WHERE
					plot_id IS NOT NULL
				AND sc.start_date <= :start_date
				)";
        }
        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }
        $sql .= ' AND (c.start_date <= :start_date OR a.start_date <= :start_date)';
        if ($options['group'] && !$counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }
        if ($options['exclude_sold_plots']) {
            $sql .= ") UNION (
            SELECT {$return}
					FROM
						{$this->contractsPlotsRelTable} cpr
					LEFT JOIN {$this->tableContracts} C ON (C . ID = cpr.contract_id)
					LEFT JOIN {$this->tableKVS} kvs ON (kvs.gid = cpr.plot_id)
					WHERE (kvs.is_edited = FALSE AND (kvs.edit_active_from <= :due_date OR kvs.edit_active_from IS NULL))
					AND	C .nm_usage_rights = 1
					AND C .is_annex = 'false'
					AND C .start_date >= (
											SELECT
												max(c.start_date)
											FROM su_contracts c
											LEFT JOIN su_contracts_plots_rel cpr1 on (cpr1.contract_id = c.id)
											WHERE cpr1.plot_id = cpr.plot_id
											AND c.nm_usage_rights = 1
										 )
					AND cpr.id not in (
											SELECT
												cpr1.id
											FROM su_contracts c
											LEFT JOIN su_contracts_plots_rel cpr1 on (cpr1.contract_id = c.id)
											WHERE c.start_date > :start_date
											AND c.nm_usage_rights = 1
										 )";

            if ($options['where']) {
                $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
            }
            if ($options['group'] && !$counter) {
                $sql .= ' GROUP BY ' . $options['group'];
            }
        }
        $sql .= "
		) UNION (
		    ( SELECT {$return}
                FROM su_contracts C
                left JOIN su_contracts a ON(a.parent_id = c.id AND a.active = true and a.start_date <= :start_date and a.due_date >= :due_date)
				INNER JOIN su_contracts_plots_rel cpr ON(cpr.contract_id = (CASE WHEN a.id IS NULL THEN c.id ELSE a.id END))
                LEFT JOIN {$this->tableKVS} kvs ON (cpr.plot_id = kvs.gid)
                LEFT JOIN {$this->plotsOwnersRelTable} po ON(po.pc_rel_id = cpr.id)
				LEFT JOIN {$this->tableOwners} o ON(o.id = po.owner_id)
				LEFT JOIN {$this->tableOwnersReps} o_r ON(o_r.id = po.rep_id)
        		LEFT JOIN {$this->viewTopicLayerByOwnerNameLabelItems} tkvs on (tkvs.gid = kvs.gid)
                WHERE (kvs.is_edited = TRUE AND kvs.edit_active_from >= :due_date) ";
        if (!$options['include_subleases']) {
            $sql .= " AND cpr.id not in (
                select pc_rel_id from
                    {$this->tableSubleasesPlotsContractsRel} scpr1
                left join su_contracts c1 on (scpr1.sublease_id = c1.id)
                where c1.start_date <= :start_date
                and c1.due_date >= :due_date
                and c1.is_sublease = true
            )";
        }
        if ($options['exclude_sold_plots']) {
            $sql .= "AND cpr.plot_id NOT IN (
				SELECT
				plot_id
				FROM
				{$this->tableSalesContracts} sc
				LEFT JOIN {$this->salesContractsPlotsRelTable} scpr ON (sc. ID = scpr.sales_contract_id)
				WHERE
					plot_id IS NOT NULL
				AND sc.start_date <= :start_date
				)";
        }
        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }
        $sql .= ' AND (c.start_date <= :start_date OR a.start_date <= :start_date)';
        if ($options['group'] && !$counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }
        $sql .= ')';
        if ($options['exclude_sold_plots']) {
            $sql .= " UNION (
            SELECT {$return}
					FROM
						{$this->contractsPlotsRelTable} cpr
					LEFT JOIN {$this->tableContracts} C ON (C . ID = cpr.contract_id)
					LEFT JOIN {$this->tableKVS} kvs ON (kvs.gid = cpr.plot_id)
					WHERE (kvs.is_edited = FALSE AND (kvs.edit_active_from <= :due_date OR kvs.edit_active_from IS NULL))
					AND	C .nm_usage_rights = 1
					AND C .is_annex = 'false'
					AND C .start_date >= (
											SELECT
												max(c.start_date)
											FROM su_contracts c
											LEFT JOIN su_contracts_plots_rel cpr1 on (cpr1.contract_id = c.id)
											WHERE cpr1.plot_id = cpr.plot_id
											AND c.nm_usage_rights = 1
										 )
					AND cpr.id not in (
											SELECT
												cpr1.id
											FROM su_contracts c
											LEFT JOIN su_contracts_plots_rel cpr1 on (cpr1.contract_id = c.id)
											WHERE c.start_date > :start_date
											AND c.nm_usage_rights = 1
										 )";

            if ($options['where']) {
                $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
            }
            if ($options['group'] && !$counter) {
                $sql .= ' GROUP BY ' . $options['group'];
            }

            $sql .= '
            )';
        }
        $sql .= ')';

        if (!$counter && $options['sort']) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($options['extent']) {
            $sql .= ') data';
        }

        if ($counter) {
            $sql .= ') data';
        }

        if ($returnOnlySQL) {
            $sql = str_replace(':start_date', "'" . $options['start_date'] . "'", $sql);

            return str_replace(':due_date', "'" . $options['due_date'] . "'", $sql);
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        $cmd->bindParameter(':start_date', $options['start_date']);
        $cmd->bindParameter(':due_date', $options['due_date']);

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getPlotsInExpiringContractsForReport($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter'] ?? false);

        $sql = '';
        if ($counter) {
            $sql = 'SELECT count(distinct(data.id)) AS count, sum(data.area) as area FROM ((SELECT DISTINCT (pc. ID), pc.contract_area AS area ';
        }
        if (!empty($options['extent'])) {
            $sql = "SELECT {$options['extent']} FROM (( select geom";
        }
        if (!$counter && empty($options['extent'])) {
            $sql = "(SELECT {$return}";
        }
        $sql .= "
            FROM
                su_contracts C
            INNER JOIN su_contracts_plots_rel pc ON (pc.contract_id = C . ID)
            INNER JOIN layer_kvs kvs ON (kvs.gid = pc.plot_id)
            LEFT JOIN su_contracts a ON (a.parent_id = c.ID AND a.is_annex = true AND (a.start_date >= c.start_date OR a.due_date <= c.due_date))
            LEFT JOIN su_contracts c_a ON (c_a.id = c.parent_id)
            LEFT JOIN su_plots_farming_rel pf ON (pf.pc_rel_id = pc. ID)
            LEFT JOIN su_plots_owners_rel po ON (po.pc_rel_id = pc. ID)
            LEFT JOIN su_owners o ON (o. ID = po.owner_id)
            LEFT JOIN su_owners_reps r ON (r. ID = po.rep_id)
            LEFT JOIN su_sales_contracts_plots_rel scpr ON (scpr.contract_id = C . ID)
            LEFT JOIN su_sales_contracts sc ON (scpr.contract_id = sc. ID)
            LEFT JOIN LATERAL (
                SELECT
                    string_AGG (
                        c1. ID || '~~' || to_char(c1.start_date, 'DD.MM.YYYY') || ' - ' || to_char(c1.due_date, 'DD.MM.YYYY') || '~~' || c1.is_annex || '~~' || c1.c_num || '~~' || c1.nm_usage_rights,
                        '__'
                    ) as c_info
                FROM
                    su_contracts c1
                LEFT JOIN su_contracts_plots_rel cpr1 ON cpr1.contract_id = c1. ID
                WHERE
                    (
                        (
                            c1.start_date >= :new_start_date
                            AND c1.start_date <= :new_due_date
                        )
                        OR (
                            c1.start_date > C .start_date
                            AND c1.due_date > C .due_date
                            AND c1.start_date <= :new_due_date
                        )
                    )
                AND pc.plot_id = cpr1.plot_id
                AND c1.nm_usage_rights <> '4'
                AND c1.active = TRUE
                AND cpr1.annex_action = 'added'
            ) new_c_info ON TRUE
            WHERE
               (kvs.is_edited = FALSE AND (kvs.edit_active_from <= :due_date OR kvs.edit_active_from IS NULL))
            AND C .nm_usage_rights <> '1'
            AND C .nm_usage_rights <> '4'
            AND C .is_sublease = 'false'
            AND C .active = 'true'
            AND c.due_date >= :start_date
            AND c.due_date <= :due_date ";
        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if (isset($options['renewed'])) {
            if ($options['renewed']) {
                $sql .= ' AND new_c_info.c_info is not null';
            } else {
                $sql .= ' AND new_c_info.c_info is null';
            }
        }
        if ($options['group']) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        $sql .= ')
                    UNION
            (SELECT ';

        if ($counter) {
            $sql .= 'DISTINCT (pc. ID), pc.contract_area AS area ';
        } else {
            $sql .= $return;
        }
        $sql .= "
            FROM
                su_contracts C
            INNER JOIN su_contracts_plots_rel pc ON (pc.contract_id = C . ID)
            INNER JOIN layer_kvs kvs ON (kvs.gid = pc.plot_id)
            LEFT JOIN su_contracts a ON (a.parent_id = c.ID AND a.is_annex = true AND (a.start_date >= c.start_date OR a.due_date <= c.due_date))
            LEFT JOIN su_contracts c_a ON (c_a.id = c.parent_id)
            LEFT JOIN su_plots_farming_rel pf ON (pf.pc_rel_id = pc. ID)
            LEFT JOIN su_plots_owners_rel po ON (po.pc_rel_id = pc. ID)
            LEFT JOIN su_owners o ON (o. ID = po.owner_id)
            LEFT JOIN su_owners_reps r ON (r. ID = po.rep_id)
            LEFT JOIN su_sales_contracts_plots_rel scpr ON (scpr.contract_id = C . ID)
            LEFT JOIN su_sales_contracts sc ON (scpr.contract_id = sc. ID)
            LEFT JOIN LATERAL (
                SELECT
                    string_AGG (
                        c1. ID || '~~' || to_char(c1.start_date, 'DD.MM.YYYY') || ' - ' || to_char(c1.due_date, 'DD.MM.YYYY') || '~~' || c1.is_annex || '~~' || c1.c_num || '~~' || c1.nm_usage_rights,
                        '__'
                    ) as c_info
                FROM
                    su_contracts c1
                LEFT JOIN su_contracts_plots_rel cpr1 ON cpr1.contract_id = c1. ID
                WHERE
                    (
                        (
                            c1.start_date >= :new_start_date
                            AND c1.start_date <= :new_due_date
                        )
                        OR (
                            c1.start_date > C .start_date
                            AND c1.due_date > C .due_date
                            AND c1.start_date <= :new_due_date
                        )
                    )
                AND pc.plot_id = cpr1.plot_id
                AND c1.nm_usage_rights <> '4'
                AND c1.active = TRUE
                AND cpr1.annex_action = 'added'
            ) new_c_info ON TRUE
            WHERE
                (kvs.is_edited = TRUE AND kvs.edit_active_from >= :due_date)
            AND C .nm_usage_rights <> '1'
            AND C .nm_usage_rights <> '4'
            AND C .is_sublease = 'false'
            AND C .active = 'true'
            AND c.due_date >= :start_date
            AND c.due_date <= :due_date";
        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if (isset($options['renewed'])) {
            if ($options['renewed']) {
                $sql .= ' AND new_c_info.c_info is not null ';
            } else {
                $sql .= ' AND new_c_info.c_info is null ';
            }
        }

        if (isset($options['group'])) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        $sql .= ')';

        if ($counter || !empty($options['extent'])) {
            $sql .= ') as data';
        }

        if (!$counter && $options['sort']) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            $sql = str_replace(':start_date', "'" . $options['start_date'] . "'", $sql);
            $sql = str_replace(':due_date', "'" . $options['due_date'] . "'", $sql);
            $sql = str_replace(':new_start_date', "'" . $options['new_start_date'] . "'", $sql);

            return str_replace(':new_due_date', "'" . $options['new_due_date'] . "'", $sql);
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        $cmd->bindParameter(':start_date', $options['start_date']);
        $cmd->bindParameter(':due_date', $options['due_date']);
        $cmd->bindParameter(':new_start_date', $options['new_start_date']);
        $cmd->bindParameter(':new_due_date', $options['new_due_date']);

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getSubleasePcRelIds($start_date, $due_date)
    {
        $sql = '
			SELECT
				array_agg(distinct (pc_rel_id)) as pc_rel_ids
			FROM
				su_subleases_plots_contracts_rel scpr1
			LEFT JOIN su_contracts c1 ON (scpr1.sublease_id = c1. ID)
			WHERE
				c1.start_date <= :due_date
			AND c1.due_date >= :start_date
			AND c1.is_sublease = TRUE
			AND c1.active = TRUE
		';

        $cmd = $this->DbModule->createCommand($sql);

        if ($returnOnlySQL) {
            $sql = str_replace($sql, ':start_date', $start_date);

            return str_replace($sql, ':due_date', $due_date);
        }

        $cmd->bindParameter(':start_date', $start_date);
        $cmd->bindParameter(':due_date', $due_date);

        return $cmd->query()->readAll();
    }

    public function getEkateNamesForContract($contractID)
    {
        $sql = "
			SELECT
				distinct( ec.ekatte_name || ' (' || kvs.ekate || ')') as zemlishte_ekatte,
				kvs.ekate
			FROM
				su_contracts_plots_rel cpr
			LEFT JOIN layer_kvs kvs ON kvs.gid = cpr.plot_id
			LEFT JOIN ekate_combobox ec on ec.ekate = kvs.ekate
			WHERE cpr.contract_id = :contract_id
			GROUP BY
				kvs.ekate,
				ec.ekatte_name
			ORDER BY
				kvs.ekate
    	";

        $cmd = $this->DbModule->createCommand($sql);

        $cmd->bindParameter(':contract_id', $contractID);

        return $cmd->query()->readAll();
    }

    public function getPlotsInSublease($params, $page = null, $rows = null, $sort = null, $order = null, $counter = false, $returnOnlySQL = false, $includeEditedPlots = false)
    {
        $subleaseId = $params['sublease_id'];
        $subleaseContractsPlotsCTEOptions = [
            'tablename' => $this->subleasesView,
            'return' => [
                'comment',
                'plot_id',
                'sublease_contract_area',
                'rent_area',
                'sublease_due_date',
                'contract_area',
                'sum(contract_area_for_sale) AS area_for_sale',
                'sales_contracts',
            ],
            'where' => [
                'sublease_id' => ['column' => 'sublease_contract_id', 'compare' => '=', 'value' => $subleaseId],
            ],
            'having' => [
                'contract_area' => ['column' => 'sum(contract_area_for_sale) < contract_area', 'compare' => '=', 'value' => true],
            ],
            'group' => 'comment, plot_id, sublease_contract_area, rent_area, sublease_due_date, contract_area, sales_contracts',
        ];

        $wrongSubleasedDataCTE = "
            , wrong_sublease_data as (
                SELECT
                    max(spa.comment::text) AS comment,
                    cpr.plot_id,
                    max(spa.contract_area) AS sublease_contract_area,
                    max(COALESCE(spa.rent_area::double precision, spa.contract_area))::numeric(10,3) AS rent_area,
                    c.due_date::date AS sublease_due_date,
                    sum(cpr.contract_area)::numeric(10,3) AS contract_area,
                    sum(sacpr.contract_area_for_sale) AS area_for_sale
                FROM 
                    su_contracts c
                LEFT JOIN su_subleases_plots_contracts_rel scpr ON scpr.sublease_id = c.id
                LEFT JOIN su_contracts_plots_rel cpr ON cpr.id = scpr.pc_rel_id
                inner JOIN su_contracts c2 ON c2.id = cpr.contract_id AND (c2.active = false or c2.active = null)
                LEFT JOIN su_subleases_plots_area spa ON c.id = spa.sublease_id AND spa.plot_id = cpr.plot_id
                LEFT JOIN su_sales_contracts_plots_rel sacpr ON sacpr.pc_rel_id = cpr.id
                where c.is_sublease = true and c.id = {$subleaseId}
                group by cpr.plot_id,c.due_date
            )
        ";

        $subleaseContractsPlotsCTESql = 'WITH sublease_contracts_plots AS (' . $this->getItemsByParams($subleaseContractsPlotsCTEOptions, false, true) . ")\n";

        $subleaseContractsPlotsCTESql .= $wrongSubleasedDataCTE;

        $options = [
            'tablename' => 'sublease_contracts_plots as scp',
            'return' => [
                'kvs.gid',
                'kvs.ekate',
                'ekatte.ekatte_name land',
                'kvs.kad_ident',
                'kvs.virtual_category_title AS category',
                'kvs.virtual_ntp_title AS area_type',
                'scp.comment',
                'kvs.allowable_area',
                'scp.plot_id',
                'scp.sublease_contract_area AS pc_area',
                'scp.rent_area',
                '(st_area(kvs.geom)/1000)::numeric(9, 3) area',
                'kvs.document_area::NUMERIC(9, 3)',
                'kvs.is_edited',
                'false as has_problem',
                'scp.sales_contracts',
            ],
            'joins' => [
                'kvs' => "LEFT JOIN {$this->tableKVS} kvs ON scp.plot_id = kvs.gid",
                'ekatte' => "LEFT JOIN {$this->tableEkatte} as ekatte ON ekatte.ekatte_code = kvs.ekate",
            ],
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => ('kad_ident' == $sort) ? $sort . ' COLLATE "alpha_numeric_bg"' : $sort,
            'order' => $order,
            'custom_counter' => 'count(*),
                sum((st_area(kvs.geom)/1000))::numeric(9, 3) area,
                sum(kvs.document_area)::numeric(9, 3) document_area,
                sum(kvs.used_area)::numeric(9, 3) used_area,
                sum(kvs.allowable_area)::numeric(9, 3) allowable_area,
                sum(scp.sublease_contract_area)::NUMERIC(9, 3) contract_area,
                sum(scp.rent_area)::NUMERIC(9, 3) rent_area,
                false as has_problem',
        ];

        $unionOptions = [
            'tablename' => 'wrong_sublease_data',
            'return' => [
                'kvs.gid',
                'kvs.ekate',
                'ekatte.ekatte_name land',
                'kvs.kad_ident',
                'kvs.virtual_category_title AS category',
                'kvs.virtual_ntp_title AS area_type',
                '\'Имот от деактивиран договор. Моля изтрийте имота от преодтадени\' AS comment',
                'kvs.allowable_area',
                'wrong_sublease_data.plot_id',
                'wrong_sublease_data.sublease_contract_area AS pc_area',
                'wrong_sublease_data.rent_area',
                '(st_area(kvs.geom)/1000)::numeric(9, 3) area',
                'kvs.document_area::NUMERIC(9, 3)',
                'kvs.is_edited',
                'true as has_problem',
                'null as sales_contracts',
            ],
            'joins' => [
                'kvs' => "LEFT JOIN {$this->tableKVS} kvs ON wrong_sublease_data.plot_id = kvs.gid",
                'ekatte' => "LEFT JOIN {$this->tableEkatte} as ekatte ON ekatte.ekatte_code = kvs.ekate",
            ],
            'group' => 'wrong_sublease_data.plot_id, kvs.gid, ekatte.ekatte_name , wrong_sublease_data.comment, wrong_sublease_data.sublease_contract_area, wrong_sublease_data.rent_area',
            'custom_counter' => 'count(*),
                sum((st_area(kvs.geom)/ 1000))::numeric(9,
                3) area,
                sum(kvs.document_area)::numeric(9,
                3) document_area,
                sum(kvs.used_area)::numeric(9,
                3) used_area,
                sum(kvs.allowable_area)::numeric(9,
                3) allowable_area,
                sum(wrong_sublease_data.sublease_contract_area)::numeric(9,
                3) contract_area,
                sum(wrong_sublease_data.rent_area)::numeric(9,
                3) rent_area,
                true as has_problem',
        ];

        if (true === $includeEditedPlots) {
            array_push(
                $options['return'],
                '
                CASE
                    when ( edit_active_from IS NOT NULL AND edit_active_from > scp.sublease_due_date) then true
                    when is_edited = false then true
                    else false
                END as is_plot_active
                '
            );

            array_push(
                $unionOptions['return'],
                'true as is_plot_active',
            );
        } else {
            $options['where']['edit_active_from'] = ['column' => '((is_edited=false AND (edit_active_from < now() OR edit_active_from ISNULL)) OR (is_edited=true AND edit_active_from>=now()))', 'compare' => '=', 'value' => true];
        }

        $sql = $subleaseContractsPlotsCTESql . '(' . $this->getItemsByParams($options, $counter, true) . ')';

        $sql .= ' UNION ALL ' . $this->getItemsByParams($unionOptions, $counter, true);

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->query()->readAll();
    }

    public function getConsolidationSourceData(string $kadIdent)
    {
        $sql = "with do_data as (
                select
                    jsonb_agg(
                        json_build_object(
                            'key', concat('DO','/',scd.kad_ident,'/',scd.id),
                            'ekatte', scd.ekatte,
                            'kad_ident', scd.kad_ident,
                            'osz_no',scd.osz_no,
                            'data',scd.data,
                            'dog_kod', scddk.value,
                            'dog_data', scd.dog_data,
                            'dog_novp', scd.dog_novp,
                            'dog_nach', scd.dog_nach,
                            'dog_kraj', scd.dog_kraj,
                            'dog_dka', scd.dog_dka,
                            'polz_ident', scd.polz_ident,
                            'polz_ime', scd.polz_ime,
                            'sobst_egn', scd.sobst_egn,
                            'sobst_ime', scd.sobst_ime,
                            'sobst_adr', scd.sobst_adr,
                            'sobst_tel',scd.sobst_tel
                        )
                    order by scd.data
                    )as data 
                from
                    su_consolidation_do scd
                left join su_consolidation_do_dog_kod scddk on
                    scddk.dog_kod = scd.dog_kod
                where kad_ident = '{$kadIdent}'
                group by scd.kad_ident
                ),
                zd_data as (
                    select 
                        jsonb_agg(
                            json_build_object(
                                'key', concat('ZD','/',scz.kad_ident,'/',scz.id),
                                'ekatte', scz.ekatte,
                                'kad_ident', scz.kad_ident,
                                'godina', concat(scz.godina-1,'/',scz.godina),
                                'data', scz.data,
                                'kod', sczk.value,
                                'dok_no', scz.dok_no,
                                'dok_data', scz.dok_data,
                                'dog_kod', sczdk.value,
                                'dog_no', scz.dog_no,
                                'dog_data', scz.dog_data,
                                'dog_nach', scz.dog_nach,
                                'dog_srok', scz.dog_srok,
                                'polz_dka', scz.polz_dka,
                                'polz_ident', scz.polz_ident,
                                'polz_ime', scz.polz_ime,
                                'polz_ekt', scz.polz_ekt,
                                'polz_adr', scz.polz_adr,
                                'polz_tel', scz.polz_tel,
                                'jelanie', sczj.value ,
                                'sluj_vkl', case when scz.sluj_vkl = 'F' then false 
                                                else true 
                                            end
                            )
                        order by scz.data
                        ) as data
                from
                    su_consolidation_zd scz
                left join su_consolidation_zd_kod sczk on
                    sczk.kod = scz.kod
                left join su_consolidation_zd_dog_kod sczdk on
                    sczdk.dog_kod = scz.dog_kod
                left join su_consolidation_zd_jelanie sczj on
                    sczj.jelanie = scz.jelanie
                where kad_ident = '{$kadIdent}'
                group by scz.kad_ident
                )
                select 
                    COALESCE(do_data.data, '[]'::JSONB)::JSONB as do_data, 
                    COALESCE(zd_data.data, '[]'::JSONB)::JSONB as zd_data
                from do_data
                FULL OUTER JOIN zd_data ON TRUE;
        ";
        $cmd = $this->DbModule->createCommand($sql);

        $result = $cmd->query()->read();

        return [
            'do_data' => json_decode($result['do_data']),
            'zd_data' => json_decode($result['zd_data']),
        ];
    }

    public function getPlotDocuments(string $kadIdent, int $year, int $page = null, int $rows = null, string $sort = null, string $order = null)
    {
        $options = [
            'tablename' => "{$this->tableKVS} AS kvs",
            'return' => [
                'row_number() OVER() AS id',
                'scd.osz_no AS document_number',
                'scddk.value AS document_type_text',
                'scddk.dog_kod AS document_type',
                'scd.dog_nach AS document_start_date',
                'scd.dog_kraj AS document_end_date',
                'scd.dog_data AS document_date',
                'scd.dog_dka AS document_area',
                'scd.polz_ime AS tenant_name',
                'scd.polz_ident AS tenant_ident',
                'scz.polz_adr AS tenant_address',
                'scz.polz_tel AS tenant_phone',
                'scd.sobst_ime AS lessor_name',
                'scd.sobst_egn AS lessor_ident',
                'scd.sobst_adr  AS lessor_address',
                'scd.sobst_tel AS lessor_phone',
            ],
            'joins' => [
                'scd' => '
                    LEFT JOIN su_consolidation_do AS scd
                        ON scd.kad_ident = kvs.kad_ident
                ',
                'scz' => '
                    LEFT JOIN su_consolidation_zd AS scz 
                        ON scz.kad_ident = kvs.kad_ident
                        AND scz.godina = scd.godina
                        AND scz.polz_ident = scd.polz_ident
                ',
                'scddk' => '
                    JOIN su_consolidation_do_dog_kod AS scddk
                        ON scddk.dog_kod = COALESCE(scd.dog_kod, 0)
                ', // IF scd.dog_kod is null join by value 0 which is 'Неизвестен договор'
            ],
            'where' => [
                'kad_ident' => [
                    'column' => 'kvs.kad_ident',
                    'compare' => '=',
                    'value' => $kadIdent,
                ],
                'scd_godina' => [
                    'column' => 'scd.godina',
                    'compare' => '=',
                    'value' => $year,
                ],
            ],
            'sort' => $sort,
            'order' => $order,
            'limit' => $rows,
            'offset' => ($page - 1) * $rows,
        ];

        $rows = $this->getItemsByParams($options, false, false);
        [$total] = $this->getItemsByParams($options, true, false);

        return [
            'rows' => $rows,
            'total' => $total['count'] ?? 0,
        ];
    }

    private function selectPlotSqlCreator($options, $return, $counter, $returnOnlySQL)
    {
        $joinKvs = 'INNER JOIN layer_kvs kvs ON(kvs.gid = pc.plot_id)';

        $sql = "SELECT {$return} FROM " . $this->tableContracts . ' c '
            . 'LEFT JOIN (SELECT DISTINCT ON(parent_id) * '
            . "FROM {$this->tableContracts} "
            . 'WHERE is_annex = true '
            . 'AND active = true '
            . 'AND start_date <= :farming_due_date '
            . 'AND due_date >= :farming_start_date ';
        if ($options['today']) {
            $sql = $sql . 'AND due_date >= :today ';
        }
        $sql = $sql . 'ORDER BY parent_id, due_date DESC) a '
            . 'ON a.parent_id = c.id'
            . ' INNER JOIN su_contracts_plots_rel pc ON(pc.contract_id = (CASE WHEN a.id IS NULL THEN c.id ELSE a.id END))'
            . ' LEFT JOIN su_contracts_plots_rel pc_c ON(pc_c.contract_id = c.id and pc_c.plot_id = pc.plot_id)'
            . ' ' . $joinKvs
            . ' LEFT JOIN su_area_types as sat ON NULLIF(kvs.area_type, \'\')::int = sat.id OR NULLIF(kvs.area_type, \'\')::int = ANY(sat.additional_codes)';
        // TODO: A dirty fix for the cases when we want to know which plots are subleased. To be refactured.
        if ($options['include_subleased_contracts']) {
            $sql = $sql . " LEFT JOIN su_subleases_plots_contracts_rel as spcr on ( (spcr.pc_rel_id = pc.id or spcr.pc_rel_id = pc_c.id) and spcr.sublease_id in (select id from su_contracts sc where sc.start_date <= '" . $options['farming_due_date'] . "' and sc.due_date >= '" . $options['farming_start_date'] . "' and sc.active = 'TRUE'))";
        }

        if (!empty($options['joins']) || is_array($options['joins'])) {
            foreach ($options['joins'] as $join) {
                $sql .= ' ' . $join . ' ';
            }
        }

        $sql = $sql . ' WHERE true';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['whereOr']) {
            $sql = $this->createWhereOrSQL($sql, $options['whereOr'], $returnOnlySQL);
        }

        if ('' != $options['pc_rel_id_string']) {
            $sql .= " AND pc.id IN({$options['pc_rel_id_string']})";
        }

        if ('' != $options['sub_pc_rel_id_string']) {
            $sql .= " AND spcr.id IN({$options['sub_pc_rel_id_string']})";
        }

        if ('' != $options['contracts_id_string']) {
            $sql .= " AND c.id IN({$options['contracts_id_string']})";
        }

        if ('' != $options['plots_id_string']) {
            $sql .= " AND kvs.gid IN({$options['plots_id_string']})";
        }

        if ('' != $options['pc_rel_anti_id_string']) {
            $sql .= " AND pc.id NOT IN({$options['pc_rel_anti_id_string']})";
        }

        if ('' != $options['sub_pc_rel_anti_id_string']) {
            $sql .= " AND spcr.id NOT IN({$options['sub_pc_rel_anti_id_string']})";
        }

        if ('' != $options['contracts_anti_id_string']) {
            $sql .= " AND c.id NOT IN({$options['contracts_anti_id_string']})";
        }

        if ('' != $options['plots_anti_id_string']) {
            $sql .= " AND kvs.gid NOT IN({$options['plots_anti_id_string']})";
        }

        if ($options['farming_start_date']) {
            $sql .= ' AND (c.start_date <= :farming_due_date OR a.start_date <= :farming_due_date)';
        }

        if ($options['farming_due_date']) {
            $sql .= ' AND (c.due_date IS NULL OR c.due_date >= :farming_start_date OR a.due_date >= :farming_start_date)';
        }
        if ($options['today']) {
            $sql .= ' AND (c.due_date IS NULL OR c.due_date >= :today OR a.due_date >= :today)';
        }

        if ($options['anketnaKartaParticipate']) {
            $sql .= ' AND ((c.nm_usage_rights != 4 and kvs.participate = false) or (c.nm_usage_rights = 4 ))';
        }

        if ($options['group'] && !$counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($options['having']) {
            $sql .= ' ' . $options['having'] . ' ';
        }

        return $sql;
    }
}
