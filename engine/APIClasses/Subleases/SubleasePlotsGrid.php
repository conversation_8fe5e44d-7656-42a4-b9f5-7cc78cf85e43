<?php

namespace TF\Engine\APIClasses\Subleases;

use DateTime;
use Prado;
use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Common\Config;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbContracts\UserDbContractsController;
use TF\Engine\Plugins\Core\UserDbOwners\UserDbOwnersController;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;
use TF\Engine\Plugins\Core\UserDbSubleases\UserDbSubleasesController;
use TF\Engine\Plugins\Core\Users\UsersController;

// Prado::using('Plugins.Core.UserDbSubleases.*');

/**
 * @rpc-module Subleases
 *
 * @rpc-service-id sublease-plots-grid
 */
class SubleasePlotsGrid extends TRpcApiProvider
{
    private $module = 'Subleases';
    private $service_id = 'sublease-plots-grid';

    /**
     * Registers the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'read']],
            'addPlots' => ['method' => [$this, 'addPlots']],
            'addSPCRelations' => ['method' => [$this, 'addSPCRelations']],
            'addConfirmedSPCRelation' => ['method' => [$this, 'addConfirmedSPCRelation']],
            'deleteSPCRelation' => ['method' => [$this, 'deleteSPCRelation']],
            'saveEditPlotAreas' => ['method' => [$this, 'saveEditPlotAreas']],
        ];
    }

    public function read($params = [], $page = null, $rows = null, $sort = null, $order = null)
    {
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);

        $counter = $UserDbPlotsController->getPlotsInSublease($params, $page, $rows, $sort, $order, true, false, true);
        $return = [
            'rows' => [],
            'total' => 0,
            'footer' => [[
                'area_type' => '<b>ОБЩО</b>',
                'used_area' => 0,
                'area' => 0,
                'document_area' => 0,
                'pc_area' => 0,
                'allowable_area' => 0,
                'rent_area' => 0,
            ]],
        ];

        if (0 == $counter[0]['count']) {
            return $return;
        }

        $result = $UserDbPlotsController->getPlotsInSublease($params, $page, $rows, $sort, $order, false, false, true);

        return [
            'rows' => $result,
            'total' => $counter[0]['count'],
            'footer' => [
                [
                    'area_type' => '<b>ОБЩО</b>',
                    'used_area' => $counter[0]['area'],
                    'area' => $counter[0]['area'],
                    'document_area' => $counter[0]['document_area'],
                    'pc_area' => $counter[0]['contract_area'],
                    'allowable_area' => $counter[0]['allowable_area'],
                    'rent_area' => $counter[0]['rent_area'],
                ],
            ],
        ];
    }

    /**
     * Reads the plots of a sublreased conract.
     *
     * @api-method read
     *
     * @param array $params {
     *                      #item string type
     *                      #item integer sublease_id
     *                      #item boolean many_contracts
     *                      #item integer sublease_farming_id
     *                      #item string  sublease_due_date
     *                      #item string  sublease_start_date
     *                      #item array  filters{
     *                      #item string  ekate
     *                      #item string  masiv
     *                      #item string  number
     *                      }
     *                      }
     * @param null|mixed $page
     * @param null|mixed $rows
     * @param null|mixed $sort
     * @param null|mixed $order
     *
     * @return array {
     *               #item array rows {
     *               #item array {
     *               #item double area
     *               #item string area_type
     *               #item string category
     *               #item double document_area
     *               #item integer gid
     *               #item string kad_ident
     *               #item string kvs_ekate
     *               #item string land
     *               #item double pc_area
     *               #item double used_area
     *               }
     *               }
     *               }
     */
    public function addPlots($params = [], $page = null, $rows = null, $sort = null, $order = null)
    {
        $type = $params['type'];
        $sublease_id = $params['sublease_id'];
        $filters = $params['filters'];
        $inManyConracts = $params['many_contracts'];
        $farming_id = $params['sublease_farming_id'];
        $sublease_due_date = $params['sublease_due_date'];
        $sublease_start_date = $params['sublease_start_date'];

        $filterEkatte = $params['filters']['ekate'];
        $filterMasiv = $params['filters']['masiv'];
        $filterNumber = $params['filters']['number'];
        $filterKadIden = $params['filters']['kad_ident'];
        $filterContractNumber = $params['filters']['c_num'];

        $UserDbController = new UserDbController($this->User->Database);
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        $UsersController = new UsersController('Users');
        $FarmingController = new FarmingController('Farming');
        $UserDbSubleasesController = new UserDbSubleasesController($this->User->Database);
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);

        if ($this->User->isGuest) {
            return [];
        }

        // create default return
        $return = [
            'rows' => [],
            'total' => 0,
            'footer' => [
                [
                    'area_type' => '<b>ОБЩО</b>',
                    'used_area' => '',
                    'rent_area' => '',
                    'area' => '',
                ],
            ],
        ];

        // check if default grid is not loaded with contract_id = 0
        if (!$sublease_id || 0 == $sublease_id) {
            return $return;
        }

        if (!$type || '' == $type) {
            return $return;
        }

        // get sublease contract_area data
        $options = [
            'tablename' => $UserDbController->DbHandler->tableSubleasesPlotsArea,
            'return' => [
                'plot_id', 'round(contract_area::numeric, 3) as contract_area', 'comment',
            ],
            'where' => [
                'sublease_id' => ['column' => 'sublease_id', 'compare' => '=', 'value' => $sublease_id],
            ],
        ];

        $sca_results = $UserDbController->getItemsByParams($options, false, false);
        $scaCount = count($sca_results);
        for ($i = 0; $i < $scaCount; $i++) {
            $contract_area_by_gid[$sca_results[$i]['plot_id']] = $sca_results[$i]['contract_area'];
            $comment_by_gid[$sca_results[$i]['plot_id']] = $sca_results[$i]['comment'];
        }
        $options = [
            'tablename' => $UserDbController->DbHandler->tableSubleasesPlotsContractsRel,
            'where' => [
                'sublease_id' => ['column' => 'sublease_id', 'compare' => '=', 'value' => $sublease_id],
            ],
        ];

        $spc_results = $UserDbController->getItemsByParams($options, false, false);
        $spcCount = count($spc_results);
        // create array/clear old data
        $pc_rel_id_array = [];
        $pc_rel_id_string = '0'; // adding default 0 value to escape empty braces in query
        if ($spcCount) {
            for ($i = 0; $i < $spcCount; $i++) {
                $pc_rel_id_array[] = $spc_results[$i]['pc_rel_id'];
            }
            $pc_rel_id_string = implode(', ', $pc_rel_id_array);
        }

        // ###
        // GET PLOTS CONTRACTS DATA
        // ###

        switch ($sort) {
            case 'kad_ident':
                $sort = 'kad_ident COLLATE "alpha_numeric_bg"';

                break;
        }

        $options = [
            'where' => [
                'nm_usage_rights' => ['column' => 'nm_usage_rights', 'compare' => '<>', 'prefix' => 'c', 'value' => 4],
                'ekate' => ['column' => 'ekate', 'compare' => '=', 'prefix' => 'ekate', 'value' => $filterEkatte],
                'masiv' => ['column' => 'masiv', 'compare' => '=', 'value' => $filterMasiv],
                'number' => ['column' => 'number', 'compare' => '=', 'value' => $filterNumber],
                'kad_ident' => ['column' => 'kad_ident', 'compare' => '=', 'prefix' => 'kvs', 'value' => $filterKadIden],
                'c_num' => ['column' => 'c_num', 'compare' => '=', 'prefix' => 'c', 'value' => $filterContractNumber],
                'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'TRUE'],
                'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                'is_annex' => ['column' => 'is_annex', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
            ],
            'custom_counter' => 'COUNT(DISTINCT(pc.id))',
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
        ];
        $result = [];

        // inner query gets contracts with their annexes
        $sublease_due_date = date('Y-m-d H:i:s', strtotime($sublease_due_date));
        $sublease_start_date = date('Y-m-d H:i:s', strtotime($sublease_start_date));
        $sublease_farming_id = $farming_id;

        $options['where']['farming'] = ['column' => 'farming_id', 'compare' => '=', 'prefix' => 'c', 'value' => $sublease_farming_id];
        $options['where']['annex_action'] = ['column' => 'annex_action', 'compare' => '=', 'prefix' => 'pc', 'value' => 'added'];

        // Filter only the plots that are not sold or are partially sold
        $options['where']['is_sold'] = [
            'column' => '(
                    sales_c.id ISNULL
                    OR (
                        sales_c.id NOTNULL AND sales_c.contract_area_for_sale < sales_c.contract_area
                    )
	            )',
            'compare' => '=',
            'value' => 'true',
        ];

        $options['where']['sublease_dates'] = [
            'column' => "(
                c.id = ANY(get_contracts_containing_plot_for_period(kvs.gid, '{$sublease_start_date}'::DATE, '{$sublease_due_date}'::DATE))
                OR a.id = ANY(get_contracts_containing_plot_for_period(kvs.gid, '{$sublease_start_date}'::DATE, '{$sublease_due_date}'::DATE))
            )",
            'compare' => '=',
            'value' => 'true',
        ];

        $options['return'] = [
            'DISTINCT(gid)', 'kad_ident', 'COALESCE(virtual_category_title, \'-\') as category', 'virtual_ntp_title as area_type', 'kvs.ekate', 'virtual_ekatte_name as land', 'kvs.is_edited',
            'used_area', 'St_Area(geom) as area', 'St_Area(geom) as area',
            "(CASE WHEN a.id IS NULL THEN c.id ELSE
            (case when ( a.start_date <= '" . $sublease_start_date . "' and a.due_date >= '" . $sublease_due_date . "') then a.id else c.id END) END) as c_id",
            "(CASE WHEN a.c_num IS NULL THEN c.c_num ELSE
            (case when ( a.start_date <= '" . $sublease_start_date . "' and a.due_date >= '" . $sublease_due_date . "') then a.c_num else c.c_num END) END)",
            "(CASE WHEN a.c_date IS NULL THEN c.c_date ELSE
            (case when ( a.start_date <= '" . $sublease_start_date . "' and a.due_date >= '" . $sublease_due_date . "') then a.c_date else c.c_date END) END)",
            'pc.id as pc_rel_id',
            'round(
                (
                    pc.contract_area - coalesce(sum(distinct sspa.contract_area), 0) - coalesce(sum(distinct sales_c.contract_area_for_sale), 0)
                )::numeric,
                3
            ) as pc_area',
            'coalesce(document_area, round((ST_Area(geom)/1000)::numeric, 3)) as document_area',
            'coalesce(sum(distinct kvs.allowable_area)::decimal(10,3), 0) as allowable_area',
            'round(
                ( 
                    pc.contract_area - coalesce(sum(distinct sspa.contract_area), 0) - coalesce(sum(distinct sales_c.contract_area_for_sale), 0) 
                ):: numeric,
                3
            ) as rent_area',
            'round(
                (
                    coalesce(sum(distinct sspa.contract_area), 0)
                ):: numeric,
                3
            ) as subleased_area',
            'round(
                (
                    pc.contract_area::numeric - coalesce(sum(distinct sales_c.contract_area_for_sale), 0)
                )::numeric,
                3
            ) as contract_area',
        ];

        $options['group'] = 'c.id, a.id, gid, pc.id, kad_ident, category, virtual_ntp_title, kvs.ekate, virtual_ekatte_name, kad_ident ,used_area, geom';
        $options['pc_rel_anti_id_string'] = $pc_rel_id_string;
        $options['start_date_on'] = $sublease_start_date;
        $options['due_date_on'] = $sublease_due_date;
        $options['custom_counter'] = 'count(distinct(gid))';

        if (array_key_exists('exclude_subleased_area', $params)) {
            // having clause should not be used when trying remove operation
            $options['having'] = 'round((pc.contract_area - coalesce((sum(sspa.contract_area)), 0))::numeric, 3) > 0';
        }

        $counter = count($UserDbPlotsController->getPlotsInSubleases($options, true, false));
        if (0 == $counter) {
            return $return;
        }

        $result = $UserDbPlotsController->getPlotsInSubleases($options, false, false);

        $total_area = 0;
        $total_used_area = 0;
        $total_pc_area = 0;
        $total_rent_area = 0;
        $total_document_area = 0;
        $total_allowable_area = 0;

        $total_area_array = [];
        $total_rent_area_array = [];
        $total_used_area_array = [];
        $total_document_area_array = [];
        $total_allowable_area_array = [];

        $resultCount = count($result);
        for ($i = 0; $i < $resultCount; $i++) {
            if ($comment_by_gid[$result[$i]['gid']]) {
                $result[$i]['comment'] = $comment_by_gid[$result[$i]['gid']];
            } else {
                $result[$i]['comment'] = '';
            }
            $result[$i]['used_area'] = round($result[$i]['used_area'], 3);
            $result[$i]['area'] = round($result[$i]['area'] / 1000, 3);

            if (!$result[$i]['pc_area']) {
                $result[$i]['pc_area'] = ($result[$i]['document_area'] < $result[$i]['area']) ? $result[$i]['document_area'] : $result[$i]['area'];
            }

            if (!in_array($result[$i]['pc_rel_id'], $total_area_array)) {
                $total_area_array[] = $result[$i]['pc_rel_id'];
                $total_area += $result[$i]['area'];
            }

            if (!in_array($result[$i]['pc_rel_id'], $total_used_area_array)) {
                $total_used_area_array[] = $result[$i]['pc_rel_id'];
                $total_used_area += $result[$i]['used_area'];
            }

            if (!in_array($result[$i]['pc_rel_id'], $total_document_area_array)) {
                $total_document_area_array[] = $result[$i]['pc_rel_id'];
                $total_document_area += $result[$i]['document_area'];
            }

            if (!in_array($result[$i]['pc_rel_id'], $total_rent_area_array)) {
                $total_rent_area_array[] = $result[$i]['pc_rel_id'];
                $total_rent_area += $result[$i]['rent_area'];
            }

            if (!in_array($result[$i]['pc_rel_id'], $total_allowable_area_array)) {
                $total_allowable_area_array[] = $result[$i]['pc_rel_id'];
                $total_allowable_area += $result[$i]['allowable_area'];
            }

            $total_pc_area += $result[$i]['pc_area'];
        }

        $return['rows'] = $result;
        $return['total'] = $counter;
        $return['footer'] = [
            [
                'area_type' => '<b>ОБЩО</b>',
                'used_area' => number_format($total_used_area, 3, '.', ''),
                'area' => number_format($total_area, 3, '.', ''),
                'document_area' => number_format($total_document_area, 3, '.', ''),
                'pc_area' => number_format($total_pc_area, 3, '.', ''),
                'allowable_area' => number_format($total_allowable_area, 3, '.', ''),
                'rent_area' => number_format($total_rent_area, 3, '.', ''),
                'allowable_area' => number_format($total_allowable_area, 3, '.', ''),
            ],
        ];

        return $return;
    }

    /**
     * Adds a plot to subleased contract.
     *
     * @api-method addSPCRelations
     *
     * @param array $params
     *                      {
     *                      #item array pc_rel_id_array
     *                      #item integer sublease_id
     *                      #item double plots_doc_area
     *                      }
     *
     * @throws TDbException
     * @throws MTRpcException
     *
     * @return array {
     *               #item string result
     *               }
     */
    public function addSPCRelations($params)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        $UsersController = new UsersController('Users');
        $FarmingController = new FarmingController('Farming');
        $UserDbSubleasesController = new UserDbSubleasesController($this->User->Database);
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);

        if ($this->User->isGuest) {
            return [];
        }

        // free pc_rel waiting confirmation data
        unset($_SESSION['pc_rel_waiting_confirmation']);
        $_SESSION['pc_rel_waiting_confirmation'] = (object) [];

        $data = $params;

        // Validate that rent area never can be bigger than contract area.
        $count = 0;
        foreach ($data['plots_contract_area'] as $plotId => $plotContractArea) {
            $subleasedPlotArea = $this->getSubleasePlotArea($plotId, $data['sublease_id']);
            $contractArea = (float) $subleasedPlotArea[0]['contract_area'];

            if (round((float)$data['plots_contract_area_sum'][$plotId] + $contractArea, 3) > round((float)$data['plots_doc_area'][$plotId], 3)) {
                throw new MTRpcException('contract_area_exceeds_plot_area', Config::CONTRACT_AREA_EXCEEDS_PLOT_AREA, $data['pc_rel_data_array'][$count]);
            }

            if (round((float)$data['plots_rent_area'][$plotId], 3) > round((float)$plotContractArea, 3)) {
                throw new MTRpcException('contract_area_exceeds_plot_area', Config::CONTRACT_RENT_AREA_EXCEEDS_PLOT_AREA, $data['pc_rel_data_array'][$count]);
            }
            $count++;
        }

        if (!$data['pc_rel_id_array'] || 0 == count($data['pc_rel_id_array']) || !$data['sublease_id'] || !(int) $data['sublease_id']) {
            return [];
        }

        $_SESSION['pc_rel_waiting_confirmation']->sublease_id = $data['sublease_id'];
        $_SESSION['pc_rel_waiting_confirmation']->plots_doc_area = $data['plots_doc_area'];
        $_SESSION['pc_rel_waiting_confirmation']->plots_contract_area = $data['plots_contract_area'];
        $_SESSION['pc_rel_waiting_confirmation']->plots_rent_area = $data['plots_rent_area'];
        $_SESSION['pc_rel_waiting_confirmation']->comments = $data['comments'];

        // get sublease data
        $options = [
            'tablename' => $UserDbController->DbHandler->tableContracts,
            'where' => [
                'contract_id' => ['column' => 'id', 'compare' => '=', 'value' => $data['sublease_id']],
            ],
        ];

        $subleaseData = $UserDbController->getItemsByParams($options, false, false);
        $subleaseStartDate = $subleaseData[0]['start_date'];
        $subleaseDueDate = $subleaseData[0]['due_date'];

        // check if plot-contract relation exists in another sublease with same period
        $options = [
            'return' => [
                'DISTINCT(spc.pc_rel_id)',
            ],
            'where' => [
                'pc_rel_id' => ['column' => 'pc_rel_id', 'compare' => 'IN', 'prefix' => 'spc', 'value' => $data['pc_rel_id_array']],
                'start_date' => ['column' => 'start_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $subleaseDueDate],
                'due_date' => ['column' => 'due_date', 'compare' => '>=', 'prefix' => 'c', 'value' => $subleaseStartDate],
            ],
        ];

        $pc_relCount = count($data['pc_rel_id_array']);

        $transaction = $UserDbController->DbHandler->DbModule->beginTransaction();
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, [], [], 'Begin transaction sublease : ');

        try {
            for ($i = 0; $i < $pc_relCount; $i++) {
                // add plot-contract relation
                $options = [
                    'tablename' => $UserDbController->DbHandler->tableSubleasesPlotsContractsRel,
                    'mainData' => [
                        'sublease_id' => $data['sublease_id'],
                        'pc_rel_id' => $data['pc_rel_id_array'][$i],
                    ],
                ];

                $new_rel_id = $UserDbController->addItem($options);

                $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['new_data' => $options], ['created_id' => $new_rel_id], 'Add sublease plot rel');

                $added_pc_rel_id_array[] = $data['pc_rel_id_array'][$i];
            }

            if (count($added_pc_rel_id_array) > 0) {
                $data['pc_rel_id_array'] = $added_pc_rel_id_array;

                // update pc_rel in contracts in case of farming contragents
                $this->updateContractPcRelData($data);
            }

            $created = $transaction->commit();
            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, [], [], 'Add sublease plot contract transaction status: ' . $created);

            unset($_SESSION['pc_rel_waiting_confirmation']);
            $_SESSION['pc_rel_waiting_confirmation'] = (object) [];
            // if all pc_rel_ids where added
            return ['result' => ''];
        } catch (\Exception $e) {
            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, [], [], 'Error!! Rolling back Transaction');
            $transaction->rollback();

            throw $e;
        }
    }

    /**
     * @api-method deleteSPCRelation
     *
     * @param array $params{
     *                       #item integer sublease_id
     *                       #item integer plot_id
     *                       }
     *
     * @throws MTRpcException
     */
    public function deleteSPCRelation($params)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        $UsersController = new UsersController('Users');
        $FarmingController = new FarmingController('Farming');
        $UserDbSubleasesController = new UserDbSubleasesController($this->User->Database);
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $UserDbContractsController = new UserDbContractsController($this->User->Database);

        $data = (object)$params;

        $options = [
            'tablename' => $UserDbController->DbHandler->tableSubleasesPlotsContractsRel,
            'where' => [
                'sublease_id' => ['column' => 'sublease_id', 'compare' => '=', 'value' => $data->sublease_id],
            ],
        ];

        $spc_results = $UserDbController->getItemsByParams($options, false, false);

        $spc_count = count($spc_results);
        if (0 == $spc_count) {
            throw new MTRpcException('NON_EXISTING_SUBLEASE_RELATION', -33209);
        }

        $subleaseOptions = [
            'tablename' => 'su_contracts',
            'where' => [
                'sublease_id' => ['column' => 'id', 'compare' => '=', 'value' => $data->sublease_id],
            ],
        ];

        $sublease = $UserDbController->getItemsByParams($subleaseOptions, false, false);

        // create array/clear old data
        $pc_rel_id_array = [];
        $pc_rel_id_string = '0'; // adding default 0 value to escape empty braces in query
        if ($spc_count) {
            for ($i = 0; $i < $spc_count; $i++) {
                $pc_rel_id_array[] = $spc_results[$i]['pc_rel_id'];
            }
            $pc_rel_id_string = implode(', ', $pc_rel_id_array);
        }

        // options for pc_rel_id query
        $options = [
            'return' => [
                'DISTINCT(pc.id) as pc_rel_id',
            ],
            'where' => [
                'plot_id' => ['column' => 'gid', 'compare' => '=', 'value' => $data->plot_id],
            ],
            'pc_rel_id_string' => $pc_rel_id_string,
        ];

        if (!empty($sublease)) {
            $options['start_date_on'] = $sublease[0]['start_date'];
            $options['due_date_on'] = $sublease[0]['due_date'];
        }

        $results = $UserDbPlotsController->getPlotsInSubleases($options, false, false);
        $resultsCount = count($results);
        if (0 == count($results)) {
            throw new MTRpcException('NON_EXISTING_CONTRACT_PLOT_RELATION', -33204);
        }

        $id_string = '';
        for ($i = 0; $i < $resultsCount; $i++) {
            $id_string .= $results[$i]['pc_rel_id'];

            if ($i < ($resultsCount - 1)) {
                $id_string .= ',';
            }
        }

        $options = [
            'tablename' => $UserDbController->DbHandler->tableSubleasesPlotsContractsRel,
            'id_string' => $id_string,
            'id_name' => 'pc_rel_id',
            'where' => [
                'sublease_id' => ['column' => 'sublease_id', 'compare' => '=', 'value' => $data->sublease_id],
            ],
        ];

        $UserDbController->deleteItemsByParams($options);
        $options1 = $options;
        $options = [
            'tablename' => $UserDbController->DbHandler->tableSubleasesPlotsArea,
            'id_string' => $data->plot_id,
            'id_name' => 'plot_id',
            'where' => [
                'sublease_id' => ['column' => 'sublease_id', 'compare' => '=', 'value' => $data->sublease_id],
                'plot_id' => ['column' => 'plot_id', 'compare' => '=', 'value' => $data->plot_id],
            ],
        ];

        $UserDbController->deleteItemsByParams($options);
        $options2 = $options;

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['request_data' => [$options1, $options2]], ['old_data' => $results], 'Deleting sublease plot rel');

        $contractsForFarmingContragents = $this->findContractsforFarmingContragents($data->sublease_id, $data->sublease_num);
        $cfcCount = count($contractsForFarmingContragents);
        for ($i = 0; $i < $cfcCount; $i++) {
            $contractID = $contractsForFarmingContragents[$i]['id'];
            $pc_rel_id = $UserDbContractsController->getContractPlotRelationID($contractID, $data->plot_id);

            if (!$pc_rel_id) {
                continue;
            }

            $options = [
                'tablename' => $UserDbController->DbHandler->contractsPlotsRelTable,
                'id_string' => $pc_rel_id,
                'id_name' => 'id',
            ];

            $UserDbController->deleteItemsByParams($options);
            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['request_data' => [$options1, $options2, $options]], ['old_data' => $results], 'Deleting sublease plot rel');
        }
    }

    /**
     * Adds a plot after confirmation to subleased contract.
     *
     * @api-method addConfirmedSPCRelation
     */
    public function addConfirmedSPCRelation()
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $data = $_SESSION['pc_rel_waiting_confirmation'];
        $count = count($data->pc_rel_id_array);

        for ($i = 0; $i < $count; $i++) {
            // add plot-contract relation
            $options = [
                'tablename' => $UserDbController->DbHandler->tableSubleasesPlotsContractsRel,
                'mainData' => [
                    'sublease_id' => $data->sublease_id,
                    'pc_rel_id' => $data->pc_rel_id_array[$i],
                ],
            ];

            $new_rel_id = $UserDbController->addItem($options);

            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['new_data' => $options], ['new_rel_id' => $new_rel_id], 'Adding confirmed SPC rel');
        }

        // update pc_rel in contracts in case of farming contragents
        $this->updateContractPcRelData($data);

        unset($_SESSION['pc_rel_waiting_confirmation']);
        $_SESSION['pc_rel_waiting_confirmation'] = (object) [];
    }

    /**
     * Saves plots on edit.
     *
     * @api-method saveEditPlotAreas
     *
     * @param array $params {
     *                      #item double contract_area
     *                      #item integer sublease_id
     *                      #item integer plot_id
     *                      #item double document_area
     *                      #item string comment
     *                      }
     *                      return void
     *
     * @throws Exception
     */
    public function saveEditPlotAreas($params)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $data = $params;

        $this->validateSubleaseAreas($data);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableSubleasesPlotsArea,
            'mainData' => [
                'contract_area' => $data['contract_area'],
                'rent_area' => empty($data['rent_area']) ? 0 : $data['rent_area'],
                'comment' => $data['comment'],
            ],
            'where' => [
                'sublease_id' => $data['sublease_id'],
                'plot_id' => $data['plot_id'],
            ],
        ];

        $UserDbController->editItem($options);

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['new_data' => $options], [], 'Editing plot area');

        $options = [
            'tablename' => $UserDbController->DbHandler->tableContracts,
            'return' => ['id'],
            'where' => [
                'from_sublease' => ['column' => 'from_sublease', 'compare' => '=', 'value' => $data['sublease_id']],
            ],
        ];
        $results = $UserDbController->getItemsByParams($options);

        if ($results[0]['id']) {
            $options = [
                'tablename' => $UserDbController->DbHandler->contractsPlotsRelTable,
                'mainData' => [
                    'contract_area' => $data['contract_area'],
                ],
                'where' => [
                    'plot_id' => $data['plot_id'],
                    'contract_id' => $results[0]['id'],
                ],
            ];

            $UserDbController->editItem($options);

            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['new_data' => $options], [], 'Editing auto created contract contract area');
        }
    }

    private function updateContractPcRelData($data)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        $UsersController = new UsersController('Users');
        $FarmingController = new FarmingController('Farming');
        $UserDbSubleasesController = new UserDbSubleasesController($this->User->Database);
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);

        $data = (object)$data;
        // get sublease data
        $options = [
            'tablename' => $UserDbController->DbHandler->tableContracts,
            'where' => [
                'contract_id' => ['column' => 'id', 'compare' => '=', 'value' => $data->sublease_id],
            ],
        ];

        $subleaseData = $UserDbController->getItemsByParams($options, false, false);
        $subleaseID = $subleaseData[0]['id'];
        $subleaseNum = $subleaseData[0]['c_num'];
        $farmingID = $subleaseData[0]['farming_id'];
        $dueDate = $subleaseData[0]['due_date'];

        $options = [
            'tablename' => $UserDbController->DbHandler->contractsPlotsRelTable,
            'return' => [
                'DISTINCT(plot_id)', 'SUM(contract_area) as contract_area',
            ],
            'where' => [
                'pc_rel_id' => ['column' => 'id', 'compare' => 'IN', 'value' => $data->pc_rel_id_array],
            ],
            'group' => 'plot_id',
        ];
        // данни за имотите от договорите за собственост от които се преотдава
        $plot_data = $UserDbController->getItemsByParams($options, false, false);

        $plotCount = count($plot_data);
        $contractsForFarmingContragents = $this->findContractsforFarmingContragents($subleaseID, $subleaseNum);

        $cfcCount = count($contractsForFarmingContragents);

        for ($i = 0; $i < $cfcCount; $i++) {
            // взема договорите създадени при добавяне на стопанство за контрагент към преотдадения договор
            $contractID = $contractsForFarmingContragents[$i]['id'];

            for ($j = 0; $j < $plotCount; $j++) {
                $options = [
                    'tablename' => $UserDbController->DbHandler->contractsPlotsRelTable,
                    'where' => [
                        'contract_id' => ['column' => 'contract_id', 'compare' => '=', 'value' => $contractID],
                        'plot_id' => ['column' => 'plot_id', 'compare' => '=', 'value' => $plot_data[$j]['plot_id']],
                    ],
                ];

                $pc_rel = $UserDbController->getItemsByParams($options, false, false);

                $options = [
                    'tablename' => $UserDbController->DbHandler->contractsPlotsRelTable,
                    'mainData' => [
                        'contract_id' => $contractID,
                        'plot_id' => $plot_data[$j]['plot_id'],
                        'contract_area' => $data->plots_contract_area[$plot_data[$j]['plot_id']],
                        'area_for_rent' => $data->plots_contract_area[$plot_data[$j]['plot_id']],
                        'contract_end_date' => $dueDate,
                    ],
                ];

                if (0 == count($pc_rel)) {
                    $pcRelID = $UserDbController->addItem($options);

                    $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['new_data' => $options], ['created_id' => $pcRelID], 'Add contract plot rel');

                    // add plot_owners_rel
                    if ($pcRelID) {
                        $options = [
                            'tablename' => $UserDbController->DbHandler->plotsFarmingRelTable,
                            'mainData' => [
                                'pc_rel_id' => $pcRelID,
                                'farming_id' => $farmingID,
                                'percent' => 100,
                            ],
                        ];

                        $rel_id = $UserDbController->addItem($options);

                        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['new_data' => $options], ['created_id' => $rel_id], 'Add plot farming rel');
                    }
                } else {
                    $options['mainData']['contract_area'] = min($data->plots_contract_area[$plot_data[$j]['plot_id']] + $pc_rel[0]['contract_area'], (float)$data->plots_doc_area[$plot_data[$j]['plot_id']]);
                    $options['mainData']['area_for_rent'] = $options['mainData']['contract_area'];
                    $options['where']['id'] = $pc_rel[0]['id'];

                    // Вземане на старите данни
                    $old_options = [
                        'tablename' => $UserDbController->DbHandler->contractsPlotsRelTable,
                        'where' => [
                            'id' => ['column' => 'id', 'compare' => '=', 'value' => $pc_rel[0]['id']],
                        ],
                    ];

                    $oldInfo = $UserDbController->getItemsByParams($old_options);

                    $UserDbController->editItem($options);

                    $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['new_data' => $options], ['old_data' => $oldInfo[0]], 'Editing sublease plot rel');
                }
            }
        }

        // dobavqne na zapis v tablicata za contract area na preotdadenite dogovori
        for ($i = 0; $i < $plotCount; $i++) {
            $plotid = $plot_data[$i]['plot_id'];

            // площ по договор от договора за собственост
            $area = (float) $data->plots_contract_area[$plotid];
            $rentArea = (float) $data->plots_rent_area[$plotid];

            $options = [
                'tablename' => $UserDbController->DbHandler->tableSubleasesPlotsArea,
                'where' => [
                    'id' => ['column' => 'plot_id', 'compare' => 'IN', 'value' => [$plotid]],
                    'sublease_id' => ['column' => 'sublease_id', 'compare' => 'IN', 'value' => [$subleaseID]],
                ],
            ];

            $exist = $UserDbController->getItemsByParams($options, true, false);
            $exist = $exist[0]['count'];

            if (!$exist) {
                $options = [
                    'tablename' => $UserDbController->DbHandler->tableSubleasesPlotsArea,
                    'mainData' => [
                        'sublease_id' => $subleaseID,
                        'plot_id' => $plotid,
                        'contract_area' => min($area, (float)$data->plots_doc_area[$plotid]),
                        'comment' => $data->comments[$plotid],
                        'rent_area' => (float)$data->plots_rent_area[$plotid],
                    ],
                ];

                $UserDbController->addItem($options);
            } else {
                $subleasePlotArea = $this->getSubleasePlotArea($plotid, $subleaseID);
                $contractArea = (float) $subleasePlotArea[0]['contract_area'];
                $prevAddedRentArea = (float) $subleasePlotArea[0]['rent_area'];

                $options = [
                    'tablename' => $UserDbController->DbHandler->tableSubleasesPlotsArea,
                    'where' => [
                        'plot_id' => $plotid,
                        'sublease_id' => $subleaseID,
                    ],
                ];

                // случай когато имот участва в няколко договора с части от площта си (не % собственост, а площ по договор)
                $options['mainData']['contract_area'] = min($contractArea + $area, (float) $data->plots_doc_area[$plotid]);
                $options['mainData']['rent_area'] = (float) $prevAddedRentArea + $rentArea;
                $options['mainData']['comment'] = $data->comments[$plotid];
                $UserDbController->editItem($options);
            }
        }
    }

    /**
     * @return array
     */
    private function getSubleasePlotArea($plotId, $subleaseID)
    {
        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableSubleasesPlotsArea,
            'return' => ['contract_area', 'rent_area'],
            'where' => [
                'id' => ['column' => 'plot_id', 'compare' => 'IN', 'value' => [$plotId]],
                'sublease_id' => ['column' => 'sublease_id', 'compare' => 'IN', 'value' => [$subleaseID]],
            ],
        ];

        return $UserDbController->getItemsByParams($options, false, false);
    }

    private function findContractsforFarmingContragents($subleaseID, $subleaseNum)
    {
        $UserDbController = new UserDbController($this->User->Database);

        // get sublease farming contragents data
        $options = [
            'tablename' => $UserDbController->DbHandler->tableContracts,
            'where' => [
                'contract_id' => ['column' => 'id', 'compare' => '=', 'value' => $subleaseID],
            ],
        ];

        $results = $UserDbController->getItemsByParams($options, false, false);
        $resultsCount = count($results);
        if (0 == $resultsCount) {
            return [];
        }

        for ($i = 0; $i < $resultsCount; $i++) {
            $farming_id_array[] = $results[$i]['farming_id'];
        }

        // get contracts for farming contragents
        $options = [
            'tablename' => $UserDbController->DbHandler->tableContracts,
            'where' => [
                'from_sublease' => ['column' => 'from_sublease', 'compare' => '=', 'value' => $subleaseID],
            ],
        ];

        return $UserDbController->getItemsByParams($options, false, false);
    }

    private function validateSubleaseAreas($params)
    {
        if ($params['rent_area'] > $params['contract_area']) {
            throw new MTRpcException('WRONG_RENT_AREA', -33229);
        }

        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);

        $startDate = $params['subleaseStartDate'];
        $dueDate = $params['subleaseDueDate'];
        $farmingId = $params['farmingId'];
        $plodId = $params['plot_id'];

        $startDate = DateTime::createFromFormat('Y-m-d', $startDate);
        $dueDateReal = DateTime::createFromFormat('Y-m-d', $dueDate);
        $dueDate = DateTime::createFromFormat('Y-m-d', $dueDate);

        $options['start_date_on'] = $subleaseStartDate = $startDate->format('Y-m-d 00:00:00');
        $options['due_date_on'] = $subleaseDueDate = $dueDate->format('Y-m-d 23:59:59');
        $options['contract_due_date_on'] = $contractSubleaseDueDate = $dueDate->format('Y-m-d 00:00:00');
        $options['disable_annex_dates'] = true;

        $options['distinct'] = 'DISTINCT ON (pc.plot_id)';

        $options['return'] = [
            'gid', 'kad_ident', 'category', 'area_type', 'ekate', 'kvs.is_edited',
            'ekattes.ekatte_name',
            'used_area', 'St_Area(geom) as area',
            'pc.plot_id',
            'pc.id as pc_rel_id',
            'array_agg(sspa.contract_area) as sublease_contracts',
            'round((CASE WHEN document_area IS NULL THEN St_Area(geom)/1000 ELSE document_area END)::numeric, 3) as document_area',
            "round( (case 
                        when a.id is null then pc.contract_area
                        else
                            case 
                                when apc.annex_action = 'removed' then 0
                                else apc.contract_area
                            end	
                    end)::numeric, 3)	as contract_area",
            'round((coalesce(sspa.contract_area,0))::numeric,3) as subleased_area',
            'COALESCE(sales_c.contract_area_for_sale, 0) AS contract_area_for_sale',
            'count(sc.id) as subleases_cnt',
        ];

        $options['where']['farming'] = ['column' => 'farming_id', 'compare' => '=', 'prefix' => 'c', 'value' => $farmingId];
        $options['where']['active'] = ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => true];
        $options['where']['annex_action'] = ['column' => 'annex_action', 'compare' => '=', 'prefix' => 'pc', 'value' => 'added'];
        $options['where']['is_sublease'] = ['column' => 'is_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => false];
        $options['where']['is_annex'] = ['column' => 'is_annex', 'compare' => '=', 'prefix' => 'c', 'value' => false];
        $options['where']['plot_id'] = ['column' => 'plot_id', 'compare' => '=', 'prefix' => 'pc', 'value' => $plodId];

        $options['caseWhere'] = " and case
                                     when 
                                        a.id is null 
                                     then 
                                        c.start_date <= '" . $subleaseStartDate . "' and (c.due_date >= '" . $contractSubleaseDueDate . "' OR c.due_date is null)
                                     else
                                        (( a.start_date <= '" . $subleaseStartDate . "' and a.due_date >= '" . $contractSubleaseDueDate . "') OR (c.start_date <= '" . $subleaseStartDate . "' and c.due_date >= '" . $subleaseDueDate . "') OR (c.start_date <= '" . $subleaseStartDate . "' and a.due_date >= '" . $contractSubleaseDueDate . "')) end";

        $options['group'] = '
            gid,ekattes.ekatte_name, pc.plot_id, sspa.contract_area, a.id, c.id, pc.contract_area, apc.annex_action, apc.contract_area,pc.id, spcr.pc_rel_id, sales_c.contract_area_for_sale
        ';

        $result = $UserDbPlotsController->getAvailablePlotsToSublease($options, false, false);

        if (!empty($result)) {
            $documentArea = floatval($params['document_area']);
            $subleasedAreaWithoutCurrentPlotArea = round(floatval($result[0]['subleased_area']) - floatval($params['currentPlotSubleasedArea']), 3);
            $futureSubleasedArea = round($subleasedAreaWithoutCurrentPlotArea + floatval($params['contract_area']), 3);

            if ($documentArea < $futureSubleasedArea && !areFloatsEqual($documentArea, $futureSubleasedArea)) {
                throw new MTRpcException('CONTRACT_AREA_EXCEEDS_PLOT_AREA', -33151);
            }
        }
    }
}
