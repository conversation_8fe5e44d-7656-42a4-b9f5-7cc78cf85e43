<?php

namespace TF\Engine\APIClasses\Payroll;

use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Common\Config;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Payroll Grid.
 *
 * @rpc-module Payroll
 *
 * @rpc-service-id payroll-exports-grid
 */
class PayrollExportsGrid extends TRpcApiProvider
{
    protected $finalFarmings = [];

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readPayrollExportsGrid'],
                'validators' => [
                    'data' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'export' => ['method' => [$this, 'requestNewPayrollExport']],
            'delete' => ['method' => [$this, 'deletePayrollExport']],
            'exportMassPayment' => ['method' => [$this, 'exportMassPayment']],
        ];
    }

    /**
     * Read payroll grid.
     *
     * @api-method read
     *
     * @param array $data -Optional params
     * @param int $page -The current page number
     * @param int $rows -Rows per page
     * @param string $sort -A grid column by which the grid is sorted
     * @param string $order -The sort order ASC/DESC
     *
     * @return array result
     *               {
     *               #item array rows                -The results.
     *               #item string total              -The count of all results.
     *               #item array footer              -The footer results.
     *               }
     */
    public function readPayrollExportsGrid(array $data, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $UsersController = new UsersController('Users');
        $FarmingController = new FarmingController('Farming');

        $options = [
            'return' => ['*'],
            'where' => [
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'value' => $this->User->GroupID],
            ],
            'sort' => 'id',
            'order' => 'ASC',
        ];

        $farming_results = $FarmingController->getFarmings($options, false, false);

        $farming_result_count = count($farming_results);
        for ($i = 0; $i < $farming_result_count; $i++) {
            $this->finalFarmings[$farming_results[$i]['id']]['name'] = $farming_results[$i]['name'];
        }

        $options = [
            'order' => $order,
            'sort' => $sort,
            'where' => [
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'value' => $this->User->GroupID],
            ],
        ];

        $counter = $UsersController->getUserPayrollExports($options, true, false);

        if (0 == $counter[0]['count']) {
            return ['rows' => [], 'total' => 0];
        }

        $options['offset'] = ($page - 1) * $rows;
        $options['limit'] = $rows;

        $results = $UsersController->getUserPayrollExports($options, false, false);
        $exportPending = false;

        $count_results = count($results);
        for ($i = 0; $i < $count_results; $i++) {
            $results[$i]['export_date'] = strftime('%d.%m.%Y %H:%M:%S', strtotime($results[$i]['export_date']));
            $results[$i]['export_params'] = json_decode($results[$i]['export_params'], true);
            $results[$i]['export_params'] = $this->formatFilterParams($results[$i]['export_params']);
            unset($results[$i]['export_params']['custom_columns']);
            switch ($results[$i]['status']) {
                case 'queued':
                    $results[$i]['status_text'] = 'Изчаква обработка';

                    break;
                case 'processing':
                    $results[$i]['status_text'] = 'Обработва се';

                    break;
                case 'processed':
                    if (strlen($results[$i]['message'])) {
                        $results[$i]['status_text'] = 'Частичен експорт';
                    } else {
                        $results[$i]['status_text'] = 'Обработен';
                    }

                    break;
                case 'error':
                    $results[$i]['status_text'] = 'Грешка при обработката';

                    break;
                case 'validation_error':
                    $results[$i]['status_text'] = 'Невалидни данни';

                    break;
            }

            $results[$i]['link'] = 'files/payrolls/' . $this->User->GroupID . '/' . $results[$i]['filename'];
            if (!$exportPending) {
                if ('queued' == $results[$i]['status'] || 'processing' == $results[$i]['status']) {
                    $exportPending = true;
                }
            }
        }

        return [
            'rows' => $results,
            'total' => $counter['0']['count'],
            'export_pending' => $exportPending,
        ];
    }

    public function requestNewPayrollExport($data)
    {
        $UsersController = new UsersController('Users');

        $options = [
            'tablename' => $UsersController->DbHandler->tablePayrollExports,
            'mainData' => [
                'export_date' => date('Y-m-d H:i:s'),
                'user_id' => $this->User->UserID,
                'group_id' => $this->User->GroupID,
                'status' => 'queued',
                'filename' => 'payroll_' . time() . '.xlsx',
                'export_params' => json_encode($data),
            ],
        ];

        return $UsersController->addCustomItem($options);
    }

    public function deletePayrollExport($id)
    {
        $UsersController = new UsersController('Users');

        $options = [
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $id],
            ],
        ];

        $results = $UsersController->getUserPayrollExports($options, false, false);
        $oldInfo = $results[0];

        if ($oldInfo['group_id'] != $this->User->GroupID) {
            throw new MTRpcException('CANNOT_MODIFY_NON_GROUP_DATA', -33224);
        }

        $options = [
            'tablename' => $UsersController->DbHandler->tablePayrollExports,
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $id],
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'value' => $this->User->GroupID],
            ],
        ];

        $UsersController->deleteItemsByParams($options);
        @unlink(PAYROLL_EXPORTS_PATH . '/' . $this->User->UserID . '/' . $oldInfo['filename']);
    }

    public function exportMassPayment($data)
    {
        $UsersController = new UsersController('Users');
        $fileName = 'mass_payment_export_';

        if (Config::MASS_PAYMENT_DSKXML === $data['masspayment_type']) {
            $fileName .= time() . '.xml';
        }

        if (Config::MASS_PAYMENT_BULBANKTXT === $data['masspayment_type']) {
            $fileName = 'NewGroupPayment_' . time() . '.txt';
        }

        $options = [
            'tablename' => $UsersController->DbHandler->tablePayrollExports,
            'mainData' => [
                'export_date' => date('Y-m-d H:i:s'),
                'user_id' => $this->User->UserID,
                'group_id' => $this->User->GroupID,
                'status' => 'queued',
                'filename' => $fileName,
                'export_params' => json_encode($data),
                'masspayment_type' => $data['masspayment_type'],
                'generate_transactions' => boolval($data['generate_transactions']),
            ],
        ];

        return $UsersController->addCustomItem($options);
    }

    protected function formatFilterParams($params)
    {
        for ($i = 0; $i < count($params['payroll_farming']); $i++) {
            $params['payroll_farming'][$i] = $this->finalFarmings[$params['payroll_farming'][$i]]['name'];
        }

        return $params;
    }
}
