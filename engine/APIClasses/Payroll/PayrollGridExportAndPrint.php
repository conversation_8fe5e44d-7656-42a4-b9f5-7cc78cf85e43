<?php

namespace TF\Engine\APIClasses\Payroll;

use DateTime;
use DOMDocument;
use Prado\Prado;
use Prado\Web\Services\TJsonRpcProtocol;
use Prado\Web\Services\TRpcApiProvider;
use Prado\Web\Services\TRpcServer;
use SimpleXMLElement;
use TF\Application\Common\Config;
use TF\Engine\Kernel\ExportToExcelClass;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\Payments\PaymentsController;
use TF\Engine\Plugins\Core\UserDbPayments\UserDbPaymentsController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Payroll Grid Export To Excel And Print.
 *
 * @rpc-module Payroll
 *
 * @rpc-service-id payroll-grid-export-print
 */
class PayrollGridExportAndPrint extends TRpcApiProvider
{
    private const RENT_NAT_PREFIX = 'nat-';
    private const PAID_RENT_NAT_PREFIX = 'paid-nat-';
    private const UNPAID_RENT_NAT_PREFIX = 'unpaid-nat-';
    private const OVERPAID_RENT_NAT_PREFIX = 'overpaid-nat-';
    public $renta_types = [];
    public $allOwnersForPayroll = [];
    public $arrayHelper;
    private $ekateData = [];
    private $footerArray = [];
    // Помощен масив, който пази всички уникални натури
    // за да може само те да са включени в експорта
    private $uniqueNatura = [
        'renta_nat_text' => [],
        'charged_renta_nat_text' => [],
        'paid_renta_by' => [],
        'paid_renta_nat' => [],
        'paid_renta_nat_by' => [],
        'paid_renta_nat_by_detailed' => [],
        'unpaid_renta_nat' => [],
        'unpaid_renta_nat_unit_value' => [],
        'over_paid_renta_nat' => [],
        'total_by_renta_nat' => [],
    ];

    // Помощен масив, който пази всички колони
    // които да бъдат сумирани при експорт
    private $summableColumns = [
        'Площ за рента',
        'Площ по договор (дка)',
        'Обработваема площ (дка)',
        'Площ за лично ползване (дка)',
        'Рента в лева',
        'Рента в евро',
        'Платена рента в лева',
        'Платена рента в евро',
        'Оставаща рента в лева',
        'Оставаща рента в евро',
        'Надплатена рента в лева',
        'Надплатена рента в евро',
    ];

    private $headers = [
        'ownerNames' => 'Собственик',
        'inheritorOf' => 'Наследник на',
        'ownerEgnEik' => 'ЕГН/ЕИК',
        'ownerPhone' => 'Телефон на собственик',
        'ownerIban' => 'IBAN на собственик',
        'repNames' => 'Представител',
        'repIban' => 'IBAN на представител',
        'contracts' => 'Договори',
        'rentPlace' => 'Място за получаване на рента',
        'plots' => 'Имоти',
        'plotDetailed' => 'Имоти - детайлно',
        'natsDetailed' => 'Натури - детайлно',
        'areaForRent' => 'Площ за рента',
        'contractArea' => 'Площ по договор (дка)',
        'arableArea' => 'Обработваема площ (дка)',
        'personalUseArea' => 'Площ за лично ползване (дка)',
        'rentMoney' => 'Рента в лева',
        'rentMoneyEuro' => 'Рента в евро',
        'paidRentMoney' => 'Платена рента в лева',
        'paidRentMoneyEuro' => 'Платена рента в евро',
        'unpaidRentMoney' => 'Оставаща рента в лева',
        'unpaidRentMoneyEuro' => 'Оставаща рента в евро',
        'overpaidRentMoney' => 'Надплатена рента в лева',
        'overpaidRentMoneyEuro' => 'Надплатена рента в евро',
        'rentInKind' => 'Рента в натура',
        'paidRentInKind' => 'Платена рента в натура',
        'unpaidRentInKind' => 'Оставаща рентента в натура',
        'overpaidRentInKind' => 'Надплатена рентента в натура',
    ];

    private $wrapTextKeys = [
        'contracts',
        'rentPlace',
        'plots',
        'rentInKind',
        'paidRentInKind',
        'unpaidRentInKind',
        'overpaidRentInKind',
        'repNames',
        'repIban',
    ];

    private $mergeRowsHeaders = [
        'paidRentMoney',
        'paidRentMoneyEuro',
        'unpaidRentMoney',
        'unpaidRentMoneyEuro',
        'overpaidRentMoney',
        'overpaidRentMoneyEuro',
        'paidRentInKind',
        'unpaidRentInKind',
        'overpaidRentInKind',
    ];

    private $usedRentaTypes = [];

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'exportToExcelPayrollGrid' => ['method' => [$this, 'exportToExcelPayrollGrid']],
            'exportToExcelPayrollPlotsGrid' => ['method' => [$this, 'exportToExcelPayrollPlotsGrid'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            // 'printPayrollGrid' => ['method' => [$this, 'printPayrollGrid']],
        ];
    }

    /**
     * Export payroll grid to excel.
     *
     * @api-method exportToExcelPayrollGrid
     *
     * @param array $data -data for export to excel
     *                    {
     *                    #item string type
     *                    #item string subtype
     *                    #item boolean is_heritor
     *                    #item string payroll_from_date
     *                    #item string payroll_to_date
     *                    #item string payroll_ekate
     *                    #item array payroll_farming
     *                    #item string farming_year
     *                    #item string owner_names
     *                    #item string rent_place
     *                    #item string egn
     *                    #item string eik
     *                    #item string company_name
     *                    #item string rep_names
     *                    #item string rep_egn
     *                    #item string rep_rent_place
     *                    #item string custom_columns
     *                    #item string print_export
     *                    #item string sort
     *                    #item string order
     *                    }
     * @param string $fileName -The file name
     *
     * @return string filePath      The file name from the database
     */
    public function exportToExcelPayrollGrid($data, $fileName)
    {
        $allDataExport = $this->getPayrollDataToExport($data);

        $this->generateHeaders($data['columns'], $allDataExport);

        $returnData = $this->formatReturnData($allDataExport, $data['columns']);

        if (true == $data['columns']['natsDetailed'] && !empty($returnData['natsValues'])) {
            $this->removeUnusedNats($returnData['natsValues']);
        }

        $footer = $this->createFooter($returnData['data']);

        foreach ($returnData['data'] as $key => $row) {
            foreach ($row as $colName => $colValue) {
                if (array_key_exists($colName, $footer[0]) && strpos($footer[0][$colName], 'SUM(')) {
                    $options['format'][$colName] = ['type' => 'number', 'value' => '#,##0.000'];
                }

                if (in_array($colName, $this->wrapTextKeys)) {
                    $options['wrapText'][] = $colName;
                }
            }

            if ($data['columns']['plotDetailed']) {
                if ($key > 0) {
                    $mergeRows = $this->generateMergingRowsOptions($allDataExport, $key, $mergeRows['startRowNum'] ?? null);
                    $options['mergeRows'] = array_merge($options['mergeRows'] ?? [], $mergeRows['results']);
                }
            }
        }

        $filePath = PAYROLL_EXPORTS_PATH . $this->User->GroupID . '/' . $fileName;
        if (!file_exists(PAYROLL_EXPORTS_PATH)) {
            mkdir(PAYROLL_EXPORTS_PATH, 0777);
        }
        if (!file_exists(PAYROLL_EXPORTS_PATH . $this->User->GroupID)) {
            mkdir(PAYROLL_EXPORTS_PATH . $this->User->GroupID, 0777);
        }

        $options['verticalAlignCenter'] = true;
        /** @var ExportToExcelClass $exportExcelDoc */
        $exportExcelDoc = new ExportToExcelClass();
        $exportExcelDoc->export($returnData['data'], $this->headers, $footer, 0, $options);
        $exportExcelDoc->saveFile($filePath);

        return SITE_URL . 'files/payrolls/' . $this->User->GroupID . '/' . $fileName;
    }

    /**
     * @api-method exportToExcelPayrollPlotsGrid
     *
     * @param array $rpcParams
     *                         {
     *                         #item string type               -The payroll type.
     *                         #item boolean is_heritor        -User is heritor ot not.
     *                         #item string payroll_from_date  -The payroll from date.
     *                         #item string payroll_to_date    -The payroll to date.
     *                         #item string payroll_ekate      -The payroll ekate.
     *                         #item string farming_year       -The farming year.
     *                         #item int owner_id              -The owner id.
     *                         #item array all_renta           -Plots contract renta and charged renta
     *                         #item string total_area
     *                         }
     * @param int $page -The current page number
     * @param int $rows -Rows per page
     * @param string $sort -A grid column by which the grid is sorted
     * @param string $order -The sort order ASC/DESC
     *
     * @return string
     */
    // public function exportToExcelPayrollPlotsGrid(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    // {
    //     $server = new TRpcServer(new TJsonRpcProtocol());
    //     $payrollGrid = new PayrollGrid($server);
    //     $time = strtotime(date('Y-m-d H:i:s'));
    //     $filename = PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID . '/payroll_plots_' . $time . '.xlsx';

    //     if (!file_exists(PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID)) {
    //         mkdir(PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID, 0777);
    //     }

    //     $headers = [
    //         'ekatte_name' => 'Землище',
    //         'kad_ident' => 'Идентификатор',
    //         'mestnost' => 'Местност',
    //         'category' => 'Категория',
    //         'area_type' => 'НТП',
    //         'all_owner_area' => 'Притежавана площ (дка)',
    //         'pu_area' => 'Площ за лично ползване (дка)',
    //         'owner_area' => 'Използвана площ (дка)',
    //         'c_type' => 'Ползване',
    //         'c_num' => 'Договор №',
    //         'rep_names' => 'Представител по договор',
    //         'sv_num' => 'Вписване №',
    //         'due_date' => 'Валиден до',
    //         'contract_renta' => 'Цена/дка',
    //         'renta' => 'Рента в лева по договор',
    //         'charged_renta' => 'Рента в лева начислена',
    //         'renta_nat' => 'Рента в натура по договор',
    //         'charged_renta_nat' => 'Рента в лева начислена',
    //         'renta_nat_type' => 'Рента в натура тип',
    //         'paid_renta' => 'Платена рента в лева',
    //         'paid_renta_by' => 'Платена рента в лева чрез',
    //         'paid_renta_nat' => 'Платена рента в натура',
    //         'paid_renta_nat_by' => 'Платена рента в натура чрез',
    //         'paid_renta_nat_by_detailed' => 'Платена рента в натура детайлно',
    //         'unpaid_renta' => 'Остатък в лева',
    //         'unpaid_renta_nat' => 'Остатък в натура',
    //         'unpaid_renta_nat_unit_value' => 'Остатък в натура по ед.ст.',
    //         'total_by_renta' => 'Общо платена рента в лева',
    //         'total_by_renta_nat' => 'Общо платена рента в натура',
    //         'iban' => 'IBAN',
    //     ];

    //     $data = $payrollGrid->readPayrollGrid($rpcParams, $page, $rows, $sort, $order);
    //     $rows = $data['rows'];
    //     $footer = $data['footer'];

    //     // change </br> with comma
    //     $rows_count = count($rows);
    //     for ($i = 0; $i < $rows_count; $i++) {
    //         $rows[$i]['renta_nat'] = str_replace('</br>', ', ', $rows[$i]['renta_nat']);
    //         $rows[$i]['charged_renta_nat'] = str_replace('</br>', ', ', $rows[$i]['charged_renta_nat']);
    //         $rows[$i]['renta_nat_type'] = str_replace('<br/>', ', ', $rows[$i]['renta_nat_type']);
    //         $rows[$i]['paid_renta_by'] = str_replace('</br>', ', ', $rows[$i]['paid_renta_by']);
    //         $rows[$i]['paid_renta_nat'] = str_replace('</br>', ', ', $rows[$i]['paid_renta_nat']);
    //         $rows[$i]['paid_renta_nat_by'] = str_replace('</br>', ', ', $rows[$i]['paid_renta_nat_by']);
    //         $rows[$i]['paid_renta_nat_by_detailed'] = str_replace('</br>', ', ', $rows[$i]['paid_renta_nat_by_detailed']);
    //         $rows[$i]['unpaid_renta'] = str_replace('</br>', ', ', $rows[$i]['plot_unpaid_renta']);
    //         $rows[$i]['unpaid_renta_nat'] = str_replace('</br>', ', ', $rows[$i]['plot_unpaid_renta_nat']);
    //         $rows[$i]['unpaid_renta_nat_unit_value'] = str_replace('</br>', ', ', $rows[$i]['plot_unpaid_renta_nat_unit_value']);
    //         $rows[$i]['total_by_renta_nat'] = str_replace('</br>', ', ', $rows[$i]['total_by_renta_nat']);
    //     }

    //     $all_owner_area = $footer[0]['all_owner_area'];
    //     $owner_area = $footer[0]['owner_area'];
    //     $pu_area = $footer[0]['pu_area'];
    //     $renta = $footer[0]['renta'];
    //     $charged_renta = $footer[0]['charged_renta'];
    //     $renta_nat = str_replace('</br>', ', ', $footer[0]['renta_nat']);
    //     $charged_renta_nat = str_replace('</br>', ', ', $footer[0]['charged_renta_nat']);
    //     $renta_nat_type = str_replace('</br>', ', ', $footer[0]['renta_nat_type']);
    //     $paid_renta = $footer[0]['paid_renta'];
    //     $paid_renta_by = str_replace('</br>', ', ', $footer[0]['paid_renta_by']);
    //     $paid_renta_nat = str_replace('</br>', ', ', $footer[0]['paid_renta_nat']);
    //     $paid_renta_nat_by = str_replace('</br>', ', ', $footer[0]['paid_renta_nat_by']);
    //     $paid_renta_nat_by_detailed = str_replace('</br>', ', ', $footer[0]['paid_renta_nat_by_detailed']);
    //     $unpaid_renta = $footer[0]['plot_unpaid_renta'];
    //     $unpaid_renta_nat = str_replace('</br>', ', ', $footer[0]['plot_unpaid_renta_nat']);
    //     $unpaid_renta_nat_unit_value = str_replace('</br>', ', ', $footer[0]['plot_unpaid_renta_nat_unit_value']);
    //     $total_by_renta = $footer[0]['total_by_renta'];
    //     $total_by_renta_nat = str_replace('</br>', ', ', $footer[0]['total_by_renta_nat']);

    //     $footerArray = [
    //         'ekatte_name' => 'ОБЩО',
    //         'kad_ident' => '',
    //         'mestnost' => '',
    //         'category' => '',
    //         'area_type' => '',
    //         'all_owner_area' => $all_owner_area,
    //         'pu_area' => $pu_area,
    //         'owner_area' => $owner_area,
    //         'c_type' => '',
    //         'c_num' => '',
    //         'sv_num' => '',
    //         'due_date' => '',
    //         'contract_renta' => '',
    //         'renta' => $renta,
    //         'charged_renta' => $charged_renta,
    //         'renta_nat' => $renta_nat,
    //         'charged_renta_nat' => $charged_renta_nat,
    //         'renta_nat_type' => $renta_nat_type,
    //         'paid_renta' => $paid_renta,
    //         'paid_renta_by' => $paid_renta_by,
    //         'paid_renta_nat' => $paid_renta_nat,
    //         'paid_renta_nat_by' => $paid_renta_nat_by,
    //         'paid_renta_nat_by_detailed' => $paid_renta_nat_by_detailed,
    //         'unpaid_renta' => $unpaid_renta,
    //         'unpaid_renta_nat' => $unpaid_renta_nat,
    //         'unpaid_renta_nat_unit_value' => $unpaid_renta_nat_unit_value,
    //         'total_by_renta' => $total_by_renta,
    //         'total_by_renta_nat' => $total_by_renta_nat,
    //     ];

    //     // add footer
    //     array_push($rows, $footerArray);

    //     $exportExcelDoc = new ExportToExcelClass();
    //     $exportExcelDoc->export($rows, $headers, []);
    //     $exportExcelDoc->saveFile($filename);

    //     return SITE_URL . 'files/uploads/export/' . $this->User->UserID . '/payroll_plots_' . $time . '.xlsx';
    // }

    /**
     * Print payroll grid.
     *
     * @api-method printPayrollGrid
     *
     * @param array $data -Data for print
     *                    {
     *                    #item string type
     *                    #item string subtype
     *                    #item boolean is_heritor
     *                    #item string payroll_from_date
     *                    #item string payroll_to_date
     *                    #item string payroll_ekate
     *                    #item array payroll_farming
     *                    #item string farming_year
     *                    #item string owner_names
     *                    #item string rent_place
     *                    #item string egn
     *                    #item string eik
     *                    #item string company_name
     *                    #item string rep_names
     *                    #item string rep_egn
     *                    #item string rep_rent_place
     *                    #item string custom_columns
     *                    #item string print_export
     *                    #item string sort
     *                    #item string order
     *                    }
     *
     * @return string dataToPrint
     */
    // public function printPayrollGrid($data)
    // {
    //     $UserDbController = new UserDbController($this->User->Database);
    //     $FarmingController = new FarmingController('Farming');

    //     // check if date is not valid
    //     if (!DateTime::createFromFormat('Y-m-d', $data['payroll_from_date']) || !DateTime::createFromFormat('Y-m-d', $data['payroll_to_date'])) {
    //         $this->Response->redirect($this->Service->constructUrl('Payments.Home'));
    //     }

    //     // get renta types
    //     $options = [
    //         'tablename' => $UserDbController->DbHandler->tableRentaTypes,
    //     ];

    //     $renta_results = $UserDbController->getItemsByParams($options, false, false);

    //     $rentCount = count($renta_results);
    //     // create renta types array
    //     for ($i = 0; $i < $rentCount; $i++) {
    //         $this->renta_types[$renta_results[$i]['id']] = $renta_results[$i]['name'] . '(' . $GLOBALS['Contracts']['renta_units'][$renta_results[$i]['unit']]['name'] . ')';
    //     }

    //     switch ($data['type']) {
    //         case 'owners':
    //             $documentData = $this->getOwnersPayrollToExportPrint($data);

    //             break;
    //         default:
    //             $this->Response->redirect($this->Service->constructUrl('Payments.Home'));

    //             break;
    //     }

    //     $documentData['rows'][] = $this->calculateFooterSummaryForPrinting($documentData, $data['separated_natura']);

    //     $ltext = $FarmingController->StringHelper->loadTemplate($GLOBALS['Templates'][11]['template'], $documentData);
    //     $ltext = '<meta charset="UTF-8"><style>@page{size: landscape;}</style>' . $ltext;

    //     return $ltext;
    // }

    public function exportMassPayments($report)
    {
        $UsersController = new UsersController();
        $inputParams = json_decode($report['export_params'], true);

        // Defensive checks to ensure required keys exist
        if (!isset($inputParams['columns']) || !is_array($inputParams['columns'])) {
            $inputParams['columns'] = [];
        }
        if (!isset($inputParams['filters']) || !is_array($inputParams['filters'])) {
            $inputParams['filters'] = [];
        }

        $inputParams['columns']['plotDetailed'] = false;
        $inputParams['columns']['inheritorOf'] = false;

        $owners = $this->getPayrollDataToExport($inputParams);

        $paymentData = $this->preparePayrollExportForPayments($owners, $inputParams);

        // Generates excel preview
        if (Config::MASS_PAYMENT_DSKXML === $report['masspayment_type']) {
            $this->exportMassPaymentAsExcelPayrollPlotsGrid($paymentData['transactions'], $report['filename']);
        }

        if (Config::MASS_PAYMENT_BULBANKTXT === $report['masspayment_type']) {
            $this->exportBulbankMassPaymentAsExcel($paymentData['transactions'], $report['filename'], $report['id']);
        }

        if (!empty($paymentData['validation_errors'])) {
            $paymentData['validation_errors'] = array_merge($paymentData['validation_errors'], $paymentData['ignored_validation_errors']);
            $UsersController->setPayrollExportMessage($report['id'], json_encode($paymentData['validation_errors'], JSON_UNESCAPED_UNICODE), 'validation_error');

            return false;
        }

        if (!empty($paymentData['ignored_validation_errors'])) {
            if (empty($paymentData['transactions'])) {
                // skip export only if no valid transtations are available
                $UsersController->setPayrollExportMessage($report['id'], json_encode($paymentData['ignored_validation_errors'], JSON_UNESCAPED_UNICODE), 'validation_error');

                return false;
            }

            $UsersController->setPayrollExportMessage($report['id'], json_encode($paymentData['ignored_validation_errors'], JSON_UNESCAPED_UNICODE));
        }

        if (Config::MASS_PAYMENT_DSKXML === $report['masspayment_type']) {
            $exportPath = $this->exportToDSKPayrollPlotsGrid($paymentData['transactions'], $report['filename']);
        }

        if (Config::MASS_PAYMENT_BULBANKTXT === $report['masspayment_type']) {
            $exportPath = $this->exportToBulbankPayrollPlotsGrid($paymentData['transactions'], $report['filename'], $report['id']);
        }

        if (true == $report['generate_transactions']) {
            $this->generatePaymentTransactios($paymentData['transactions']);
        }

        return $exportPath;
    }

    public function exportBulbankMassPaymentAsExcel(array $paymentData, string $filename, int $reportId): string
    {
        $exportData = [];
        $senderData = $paymentData[0];
        $totalAmount = number_format(array_sum(array_column($paymentData, 'sum')), 2, '.', '');

        $headers = [
            'date' => 'Дата на писмото',
            'reference' => 'Референция (Изх. №)',
            'payment_date' => 'Дата на плащане',
            'payment_end_date' => 'Крайна дата на плащане',
            'total_sum' => 'Обща сума',
            'senders_iban' => 'Фирмена сметка',
            'currency' => 'Код Валута',
            'details_line1' => 'Основание за плащане',
        ];
        $paymentDate = date('d/m/Y', time());
        $paymentEndDate = date('d/m/Y', strtotime(' + 2 week'));
        $refDate = date('d.m.y', time());

        foreach ($paymentData as $key => $payment) {
            if (0 == $key) {
                $exportData[$key] = [
                    'date' => $paymentDate,
                    'reference' => $reportId . '/' . $refDate,
                    'payment_date' => $paymentDate,
                    'payment_end_date' => $paymentEndDate,
                    'total_sum' => $totalAmount,
                    'senders_iban' => $senderData['senders_iban'],
                    'currency' => 'BGN',
                    'details_line1' => '',
                ];

                $exportData[$key + 1] = [
                    'date' => 'ЕГН',
                    'reference' => 'Име',
                    'payment_date' => 'Презиме',
                    'payment_end_date' => 'Фамилия',
                    'total_sum' => 'Сума за получаване',
                    'senders_iban' => 'IBAN Сметка на получател',
                    'currency' => 'BIC код на Банка',
                    'details_line1' => 'Основание за плащане',
                ];
            }

            $exportData[$key + 2] = [
                'date' => $payment['recipient_eik'],
                'total_sum' => $payment['sum'],
                'senders_iban' => $payment['recipient_iban'],
                'currency' => $payment['recipient_bic'],
                'details_line1' => 'НАЕМ ЗЕМЕДЕЛСКА ЗЕМЯ',
            ];

            [$fistname, $surname, $lastname] = explode(' ', $payment['recipient_full_name']);

            $exportData[$key + 2]['reference'] = $fistname;
            $exportData[$key + 2]['payment_date'] = $surname;
            $exportData[$key + 2]['payment_end_date'] = $lastname;
        }

        $path = PAYROLL_EXPORTS_PATH . '/' . $this->User->GroupID;
        $xlsPath = $path . '/' . substr($filename, 0, -3) . 'xlsx';

        /** @var ExportToExcelClass */
        $exportExcelDoc = Prado::getApplication()->getModule('ExportToExcel');

        $exportExcelDoc->export($exportData, $headers, []);
        $exportExcelDoc->saveFile($xlsPath);

        return 'files/payrolls/' . $this->User->GroupID . '/' . $filename;
    }

    public function exportMassPaymentAsExcelPayrollPlotsGrid($paymentData, $filename)
    {
        $headers = [
            'reference' => 'Референция №',
            'date' => 'Дата (ггммдд)',
            'senders_iban' => 'Подател IBAN',
            'senders_name' => 'Подател име',
            'senders_bic' => 'Подател BAE',
            'senders_bank_name' => 'Подател банка',
            'sum' => 'Сума',
            'recipient_iban' => 'Получател IBAN',
            'recipient_name' => 'Получател име',
            'recipient_bic' => 'Получател BAE',
            'recipient_bank_name' => 'Получател банка',
            'details_line1' => 'Информация',
            'details_line2' => 'Допълнителна информация',
        ];

        $sum = 0;
        foreach ($paymentData as $payment) {
            $sum += $payment['sum'];
        }

        $footer = [
            [
                'senders_bank_name' => 'ОБЩО:',
                'sum' => $sum,
                'recipient_iban' => 'лв.',
            ],
        ];

        $path = PAYROLL_EXPORTS_PATH . $this->User->GroupID;
        if (!file_exists($path)) {
            mkdir($path, 0777);
        }

        $xlsPath = $path . '/' . substr($filename, 0, -3) . 'xlsx';

        /** @var ExportToExcelClass */
        $exportExcelDoc = Prado::getApplication()->getModule('ExportToExcel');
        $exportExcelDoc->export($paymentData, $headers, $footer);
        $exportExcelDoc->saveFile($xlsPath);

        return 'files/payrolls/' . $this->User->GroupID . '/' . $filename;
    }

    private function generateMergingRowsOptions(array $allDataExport, int $key, $startRowNum = null): array
    {
        $mergeRows = [];
        $endRowNum = null;
        $rowNum = $key + 1;

        if (!$startRowNum && $allDataExport[$key - 1]['contract_id'] == $allDataExport[$key]['contract_id']) {
            $startRowNum = $rowNum;
        }
        if ($startRowNum && (!isset($allDataExport[$key + 1]) || $allDataExport[$key + 1]['contract_id'] != $allDataExport[$key]['contract_id'])) {
            $endRowNum = $rowNum + 1;
        }

        if ($startRowNum && $endRowNum) {
            foreach ($this->mergeRowsHeaders as $header) {
                $mergeRows[] = [
                    'startRow' => $startRowNum,
                    'endRow' => $endRowNum,
                    'col' => $this->getColumnLetterByHeader($header),
                ];
            }
            $startRowNum = null;
            $endRowNum = null;
        }

        return [
            'results' => $mergeRows,
            'startRowNum' => $startRowNum,
        ];
    }

    private function generatePaymentTransactios($paymentData)
    {
        $defaultValues = [
            'payment_type_money' => true,
            'payment_type_natura' => false,
            'payment_date' => date('Y-m-d'),
            'payment_natura_type' => null,
            'payment_natura_price' => null,
            'payment_natura_amount' => null,
            'payment_method_bank' => true,
            'payment_method_post_order' => false,
            'payment_order' => true,
            'weighing_note' => null,
        ];

        $paymentsRpc = makeApiClass('payments-rpc', 'contract-add-payment');

        foreach ($paymentData as $payment) {
            $data = $defaultValues;
            $data['payment_amount'] = $payment['sum'];
            $data['payment_recipient'] = $payment['recipient_full_name'];
            $data['payment_recipient_egn'] = $payment['recipient_eik'];
            $data['payment_bank_account'] = $payment['recipient_iban'];

            $paymentsRpc->savePayment($payment['payment_data'], $data);
        }
    }

    private function preparePayrollExportForPayments($payrollData, $inputParams)
    {
        $FarmingController = new FarmingController('Farming');

        $inputParams['separated_natura'] = false;
        $transactions_count = count($payrollData);
        if ($transactions_count < 1) {
            throw new MTRpcException('Error Processing Request', 1);
        }

        $paymentData = [
            'validation_errors' => [],
            'ignored_validation_errors' => [],
            'transactions' => [],
        ];

        $senders_iban = $inputParams['payer_iban'];
        $farmRecord = array_filter($payrollData, function ($farming) use ($inputParams) {
            return $farming['farming_id'] == $inputParams['farming_id'];
        });
        $firstFarmRecord = reset($farmRecord);
        $senders_name = strtoupper($firstFarmRecord['farming_name']);

        if (empty($senders_iban) || 22 !== strlen($senders_iban) || 'BG' !== substr($senders_iban, 0, 2)) {
            $paymentData['validation_errors'][] = strtr(
                'Липсващ или невалиден IBAN на наредителя! Наредител: :name, IBAN: :iban',
                [
                    ':name' => $senders_name,
                    ':iban' => $senders_iban,
                ]
            );
        }

        $tmpBankCode = substr($inputParams['payer_iban'], 4, 8);
        $tmpBankInfo = $this->getBankInfoFromBankCode($tmpBankCode);

        if (empty($tmpBankInfo)) {
            $paymentData['validation_errors'][] = strtr(
                'Невалиден IBAN на наредителя! Наредител: :name, IBAN: :iban',
                [
                    ':name' => $senders_name,
                    ':iban' => $senders_iban,
                ]
            );
        }

        $senders_bic = $tmpBankInfo['bank_code'];
        $senders_swift = $tmpBankInfo['bic_code'];
        $senders_bank_name = $tmpBankInfo['bank_name_en'];
        $senders_bank_name = strtoupper($FarmingController->StringHelper->trimStringToLength($senders_bank_name, 35, false));

        foreach ($payrollData as $ownerData) {
            if ($ownerData['renta'] <= 0) {
                continue;
            }

            $tmpContracts = str_replace('"', '', $ownerData['c_num']);
            $tmpContracts = str_replace("'", '', $tmpContracts);

            $sum = number_format($ownerData['renta'], 2, '.', '');

            $recipient_iban = trim($ownerData['rep_iban'] ?? $ownerData['iban']);
            $recipient_egn_eik = $ownerData['rep_egn'] ? $ownerData['rep_egn'] : $ownerData['egn_eik'];
            $recipient_name_original = $ownerData['rep_names'] ? $ownerData['rep_names'] : $ownerData['owner_names'];

            $recipient_name = strtoupper($FarmingController->StringHelper->trimStringToLength($recipient_name_original, 35, false));

            if (empty($recipient_iban) || 22 !== strlen($recipient_iban) || 'BG' !== substr($recipient_iban, 0, 2)) {
                $paymentData['ignored_validation_errors'][] = strtr(
                    'Липсващ или невалиден IBAN на получателя! Получател: :name, ЕГН/ЕИК: :egn_eik, IBAN: :iban',
                    [
                        ':egn_eik' => $recipient_egn_eik,
                        ':name' => $recipient_name_original,
                        ':iban' => $recipient_iban,
                    ]
                );

                continue;
            }

            $tmpRecipientBankCode = substr($recipient_iban, 4, 8);
            $tmpRecipientBankInfo = $this->getBankInfoFromBankCode($tmpRecipientBankCode);

            if (empty($tmpRecipientBankInfo)) {
                $paymentData['ignored_validation_errors'][] = strtr(
                    'Невалиден IBAN на получателя! Получател: :name, ЕГН/ЕИК: :egn_eik, IBAN: :iban',
                    [
                        ':egn_eik' => $recipient_egn_eik,
                        ':name' => $recipient_name_original,
                        ':iban' => $recipient_iban,
                    ]
                );

                continue;
            }

            $recipient_bic = $tmpRecipientBankInfo['bic_code'];

            $recipient_bank_name = strtoupper($FarmingController->StringHelper->trimStringToLength($tmpRecipientBankInfo['bank_name_en'], 35, false));
            $details_line1 = 'PLASHTANE PO DOGOWORI';
            $details_line2 = strtoupper($FarmingController->StringHelper->convertToLatin($tmpContracts));
            $details_line2 = $FarmingController->StringHelper->trimStringToLength($details_line2, 35, true);

            if (strlen($details_line2) < 1) {
                $details_line2 = '...';
            }

            $ownerNames = explode(' ', $recipient_name_original);

            $transaction['date'] = $inputParams['order_date'];
            $transaction['senders_iban'] = $senders_iban;
            $transaction['senders_name'] = $senders_name;
            $transaction['senders_bic'] = $senders_bic;
            $transaction['senders_swift'] = $senders_swift;
            $transaction['senders_bank_name'] = $senders_bank_name;
            $transaction['sum'] = $sum;
            $transaction['recipient_bic'] = $recipient_bic;
            $transaction['recipient_bank_name'] = $recipient_bank_name;
            $transaction['recipient_iban'] = $recipient_iban;
            $transaction['recipient_name'] = $recipient_name;
            $transaction['recipient_eik'] = $recipient_egn_eik;
            $transaction['details_line1'] = $details_line1;
            $transaction['details_line2'] = $details_line2;
            $transaction['recipient_first_name'] = $ownerNames[0] ?? '';
            $transaction['recipient_surname'] = $ownerNames[1] ?? '';
            $transaction['recipient_lastname'] = $ownerNames[2] ?? '';
            $transaction['recipient_full_name'] = $recipient_name_original;
            $transaction['owner_type'] = $ownerData['owner_type'];

            $transaction['payment_data'] = [
                'payment_type' => 'owner_payments',
                'year' => $inputParams['filters']['farming_year'],
                'owner_array' => [
                    [
                        'owner_id' => $ownerData['owner_id'],
                        'path' => $ownerData['path'] ?? null,
                        'contract_id' => $ownerData['contract_id'],
                        'owner_area' => $ownerData['total_area_contract'][$ownerData['contract_id']],
                        'charged_renta' => $ownerData['charged_renta'],
                        'renta' => $ownerData['renta'],
                        'unpaid_renta' => $ownerData['renta'],
                        'is_heritor' => $ownerData['is_heritor'],
                        'farming_id' => $ownerData['farming_id'],
                    ],
                ],
            ];
            $paymentData['transactions'][] = $transaction;
        }

        return $paymentData;
    }

    private function getPayrollDataForExport($filter)
    {
        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableRentaTypes,
        ];

        $renta_results = $UserDbController->getItemsByParams($options, false, false);
        $this->setEkateData();
        // create renta types array
        $count_renta_result = count($renta_results);
        for ($i = 0; $i < $count_renta_result; $i++) {
            $this->renta_types[$renta_results[$i]['id']] = $renta_results[$i]['name'] . '(' . $GLOBALS['Contracts']['renta_units'][$renta_results[$i]['unit']]['name'] . ')';
        }

        $allDataExport = $this->getOwnersPayrollToExportPrint($filter);

        return $allDataExport;
    }

    private function exportToBulbankPayrollPlotsGrid($paymentData, $filename, $reportId): string
    {
        $senderData = $paymentData[0];
        $totalAmount = number_format(array_sum(array_column($paymentData, 'sum')), 2, '.', '');
        $content = $header = '';

        $paymentDate = date('d/m/Y', time());
        $paymentEndDate = date('d/m/Y', strtotime(' + 2 week'));
        $refDate = date('d.m.Y', time());
        $reference = str_pad('1/' . $refDate, 12, ' ', STR_PAD_RIGHT);
        $paymentsCount = str_pad(count($paymentData), 5, '0', STR_PAD_LEFT);
        foreach ($paymentData as $key => $payment) {
            if (0 == $key) {
                $header = 'FIX WIN BULBANK %s %s %s %s %s %s %s %s %s';
                $headerData = [
                    $paymentDate,
                    $reference,
                    $paymentDate,
                    $paymentEndDate,
                    $paymentsCount,
                    str_pad($totalAmount, 12, ' ', STR_PAD_RIGHT),
                    $senderData['senders_iban'],
                    'BGN',
                    str_pad('', 35, ' ', STR_PAD_RIGHT),
                ];

                $content = sprintf($header, ...$headerData) . PHP_EOL;
            }

            $nameConcat = $payment['recipient_full_name'];

            if (mb_strlen($nameConcat, 'utf-8') < 35) {
                $clientName = $nameConcat . str_repeat(' ', 35 - mb_strlen($nameConcat, 'utf-8'));
            }

            $note = 'НАЕМ ЗЕМЕДЕЛСКА ЗЕМЯ' . str_repeat(' ', 70 - mb_strlen('НАЕМ ЗЕМЕДЕЛСКА ЗЕМЯ', 'utf-8'));

            $contentStr = '%s %s %s %s %s %s';
            $contentData = [
                $payment['recipient_iban'],
                str_pad($payment['recipient_eik'], 10, ' ', STR_PAD_RIGHT),
                $clientName,
                str_pad($payment['sum'], 12, ' ', STR_PAD_RIGHT),
                $note,
                $payment['recipient_bic'],
            ];

            $content .= sprintf($contentStr, ...$contentData) . PHP_EOL;
        }

        // $content = iconv(mb_detect_encoding($content), 'cp1251', $content);
        $content = mb_convert_encoding($content, 'Windows-1251', 'UTF-8');

        $filename = pathinfo($filename, PATHINFO_FILENAME);
        $groupId = Prado::getApplication()->getUser()->GroupID;
        $exportPath = PAYROLL_EXPORTS_PATH . "{$groupId}";

        if (!file_exists($exportPath)) {
            mkdir($exportPath, 0755, true);
        }

        $path = "{$exportPath}/{$filename}.txt";

        file_put_contents($path, $content);

        return $path;
    }

    private function exportToDSKPayrollPlotsGrid($paymentData, $filename)
    {
        $count = count($paymentData);
        $senderData = $paymentData[0];
        $totalAmount = number_format(array_sum(array_column($paymentData, 'sum')), 2, '.', '');

        $doc = new SimpleXMLElement('<?xml version="1.0" encoding="UTF-8" ?><Payments></Payments>');

        $header = $doc->addChild('Header');
        $header->addChild('CustomerName', $senderData['senders_name']);
        $header->addChild('DocumentAccount', $senderData['senders_iban']);
        $header->addChild('DocumentBankCode', $senderData['senders_swift']);
        $header->addChild('DocumentBankName', $senderData['senders_bank_name']);
        $header->addChild('Currency', 'BGN');
        $header->addChild('TotalAmount', $totalAmount);
        $header->addChild('TotalRows', $count);
        $rows = $doc->addChild('Rows');

        foreach ($paymentData as $payment) {
            if (22 != strlen($payment['recipient_iban'])) {
                throw new MTRpcException('INVALID_IBAN_LENGTH', -33225);
            }

            $creditPayment = $rows->addChild('CP');
            $creditPayment->addChild('Amount', $payment['sum']);
            $creditPayment->addChild('Reason', $payment['details_line1']);
            $creditPayment->addChild('Reason2', $payment['details_line2']);
            $creditPayment->addChild('Charges', 'OUR');
            $creditPayment->addChild('ProcessingDate', $payment['date']);
            $creditPayment->addChild('Payroll', 'false');
            $creditPayment->addChild('MassPayment', 'true');
            $creditPayment->addChild('Aviso', 'false');
            $creditPayment->addChild('ExternalReference', '');
            $creditPayment->addChild('PaymentSystem', 'BISERA');
            $beneficiary = $creditPayment->addChild('Beneficiary');
            $beneficiary->addChild('Name', $payment['recipient_name']);
            $beneficiary->addChild('IBAN', $payment['recipient_iban']);
            $beneficiary->addChild('Address', '');
            $beneficiary->addChild('Town', '');
            $beneficiary->addChild('BIC', $payment['recipient_bic']);
            $beneficiary->addChild('BankName', $payment['recipient_bank_name']);
        }

        libxml_use_internal_errors(true);
        $xsd = file_get_contents(TEMPLATE_PATH . 'DSKBGNPaymentsXSD.xsd');

        if (empty($xsd)) {
            throw new MTRpcException('DSKBGNPaymentsXSD.xsd file not found or file is empty. Path to file: ' . TEMPLATE_PATH . 'DSKBGNPaymentsXSD.xsd', -33225);
        }

        $filename = pathinfo($filename, PATHINFO_FILENAME);
        $groupId = Prado::getApplication()->getUser()->GroupID;
        $exportPath = PAYROLL_EXPORTS_PATH . "{$groupId}";

        if (!file_exists($exportPath)) {
            mkdir($exportPath, 0755, true);
        }

        $path = "{$exportPath}/{$filename}.xml";
        $docXml = $doc->asXML();
        file_put_contents($path, $docXml);
        $domDoc = new DOMDocument();
        $domDoc->loadXML($docXml);

        if (!file_exists($path)) {
            throw new MTRpcException('Mass export is not exists in: ' . $path, -33229);
        }

        if (!$domDoc->schemaValidateSource($xsd)) {
            $error = libxml_get_last_error();
            if (false === strpos($error->message, 'ProcessingDate')) {
                throw new MTRpcException('DSK XML ERROR: ' . $error->message, -33229);
            }
        }

        return $path;
    }

    private function getBankInfoFromBankCode($code)
    {
        $UsersController = new UsersController('Users');

        $options = [
            'tablename' => 'su_banks',
            'where' => [
                'bank_code' => ['column' => 'bank_code', 'compare' => '=', 'value' => $code],
            ],
        ];

        $bankDetails = $UsersController->getItemsByParams($options);

        return $bankDetails[0];
    }

    private function getPayrollDataToExport($data)
    {
        $result = [];
        $paymentsController = new PaymentsController($this->User->Database);

        $payrollData = $paymentsController->getOwnersPayroll($data['filters']['farming_year'], $data['filters']);
        $exportData = $this->getLeafOwners($payrollData['rows']);

        if (true == $data['columns']['plotDetailed']) {
            $exportData = $this->separateOwnersByPlots($data['filters']['farming_year'], $exportData);
        }

        $result = $this->aggregateOwners($exportData, $data['columns']['inheritorOf'], $data['columns']['plotDetailed']);

        return $paymentsController->formattingOwnersData($result);
    }

    // private function getOwnersPayrollToExportPrint($data)
    // {
    //     $server = new TRpcServer(new TJsonRpcProtocol());
    //     $payrollGrid = new PayrollGrid($server);
    //     $FarmingController = new FarmingController('Farming');

    //     if (!empty($data['owner_egns']) && is_array($data['owner_egns'][0])) {
    //         $data['owner_egns'] = array_column($data['owner_egns'], 'value');
    //     }

    //     if (!empty($data['company_eiks']) && is_array($data['company_eiks'][0])) {
    //         $data['company_eiks'] = array_column($data['company_eiks'], 'value');
    //     }

    //     // make array to object
    //     $this->arrayHelper = $FarmingController->ArrayHelper;
    //     $this->payrollExportFarmingYear = $data['farming_year'];
    //     $results = $payrollGrid->readPayrollGrid($data);

    //     $data = (object)$data;

    //     $rows = $results['rows'];
    //     $rows_count = count($rows);
    //     for ($i = 0; $i < $rows_count; $i++) {
    //         $rows[$i]['number'] = $i + 1;
    //         $rows[$i]['farming_year'] = $this->payrollExportFarmingYear;
    //         $this->allOwnersForPayroll[] = $rows[$i];
    //         $this->_getChildren($rows[$i], $i + 1);
    //     }

    //     $data->farming_year = $GLOBALS['Farming']['years'][$data->farming_year]['farming_year'];

    //     // if is checked summary print
    //     if ('summary' == $data->subtype) {
    //         $summaryArray = $this->getPayrollData($data->subtype);
    //         $return['rows'] = $summaryArray;
    //         $return['title'] = 'Ведомост - Сумарно (без починалите) | ' . $data->farming_year;
    //     } elseif ('owners' == $data->subtype) {
    //         $summaryArray = $this->getPayrollData($data->subtype);
    //         $return['rows'] = $summaryArray;
    //         $return['title'] = 'Ведомост - Сумарно (без наследници) | ' . $data->farming_year;
    //     } elseif ('detailed' == $data->subtype) {
    //         // GPS-4765 This export type is hidden in FE
    //         $detailedArray = $this->getDetailedPayrollData($payrollGrid, $this->allOwnersForPayroll);
    //         $return['rows'] = $detailedArray;
    //         $return['title'] = 'Ведомост - Разделно (без починалите) | ' . $data->farming_year;
    //     }

    //     $existedColumns = [
    //         'Обща притежавана площ' => 'all_owner_area',
    //         'Площ за лично ползване (дка)' => 'pu_area',
    //         'Обща площ за рента' => 'owner_area',
    //         'Обработваема площ (дка)' => 'cultivated_area',
    //         'Телефон на собственик' => 'phone',
    //         'Мобилен на собственик' => 'mobile',
    //         'Договори' => 'c_num',
    //         'Представител по договор' => 'rep_names',
    //         'Имоти' => 'plots_name',
    //         'Рента в лева по договор (сума)' => 'renta',
    //         'Начислена рента в лева (сума)' => 'charged_renta',
    //         'Рента в натура по договор (общо)' => 'renta_nat_text',
    //         'Начислена рента в натура (общо)' => 'charged_renta_nat_text',
    //         'Платена рента в лева (сума)' => 'paid_renta',
    //         'Платена рента в лева чрез' => 'paid_renta_by',
    //         'Платена рента в натура (общо)' => 'paid_renta_nat',
    //         'Платена рента в натура чрез' => 'paid_renta_nat_by',
    //         'Платена рента в натура детайлно' => 'paid_renta_nat_by_detailed',
    //         'Остатък в лева' => 'unpaid_renta',
    //         'Остатък в натура' => 'unpaid_renta_nat',
    //         'Остатък в натура по ед.ст.' => 'unpaid_renta_nat_unit_value',
    //         'Надплатена рента в лева' => 'over_paid',
    //         'Надплатена рента в натура' => 'over_paid_renta_nat',
    //         'Общо платена рента в лева' => 'total_by_renta',
    //         'Общо платена рента в натура' => 'total_by_renta_nat',
    //         'Място за получаване на рента' => 'rent_place',
    //         'Информация от служба по вписвания' => 'sv_num',
    //         'IBAN' => 'iban',
    //     ];

    //     $return['header'] = [
    //         'number' => ['text' => 'Номер', 'width' => '40'],
    //         'owner_names' => ['text' => 'Собственик', 'width' => '200'],
    //         'egn_eik' => ['text' => 'ЕГН/ЕИК', 'width' => '100'],
    //         'all_owner_area' => ['text' => 'Обща притежавана площ', 'width' => '40'],
    //         'pu_area' => ['text' => 'Площ за лично ползване (дка)', 'width' => '40'],
    //         'owner_area' => ['text' => 'Обща площ за рента', 'width' => '40'],
    //         'cultivated_area' => ['text' => 'Обработваема площ (дка)', 'width' => '40'],
    //     ];

    //     $customColumns = explode(':sep:', trim($data->custom_columns, '{}'));
    //     $customColumns = $this->arrayHelper->filterEmptyStringArr($customColumns);
    //     $customColumnsCount = count($customColumns);
    //     for ($i = 0; $i < $customColumnsCount; $i++) {
    //         if (array_key_exists($customColumns[$i], $existedColumns)) {
    //             $return['header'][$existedColumns[$customColumns[$i]]] = [
    //                 'text' => $customColumns[$i],
    //                 'width' => '150',
    //             ];
    //         } else {
    //             $return['header']['custom_culumn' . $i] = [
    //                 'text' => $customColumns[$i],
    //                 'width' => '80',
    //             ];
    //         }
    //     }
    //     // Ако експортът е с разделени натури се променят headers
    //     // на колоните, които да бъдат включени в експорта
    //     if ($data->separated_natura) {
    //         // Добавяне на новите колони и поставяне на правилното им място
    //         $return['header'] = $this->transformSeparateRentaColumnsToCellArrays($return['header']);
    //         // След като са добавени новите headers се добавят колоните, които да бъдат сумирани
    //         $this->getSummableColumns($return['header']);
    //     }

    //     $return['header']['date'] = ['text' => 'Дата', 'width' => '50'];
    //     $return['header']['received_by'] = ['text' => 'Получил', 'width' => '100'];
    //     $return['header']['signature'] = ['text' => 'Подпис', 'width' => '50'];

    //     return $return;
    // }

    /**
     * Returns the data for summary payroll(without the dead people or without the heritors).
     *
     * @param string $type Type of print|export(Сумарно (без починалите), Сумарно (без наследници))
     *
     * @return array
     */
    // private function getPayrollData($type)
    // {
    //     if ('summary' == $type) {
    //         $without = 'is_dead';
    //     } elseif ('owners' == $type) {
    //         $without = 'is_heritor';
    //     } else {
    //         return [];
    //     }

    //     $UsersController = new UsersController('Users');
    //     $totalRentaNatTextForXlsArray = [];
    //     $totalChargedRentaNatTextForXlsArray = [];
    //     $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);

    //     // calculate all renta for every owner
    //     $allOwnersForPayrollCount = count($this->allOwnersForPayroll);

    //     for ($i = 0; $i < $allOwnersForPayrollCount; $i++) {
    //         $ownerID = $this->allOwnersForPayroll[$i]['owner_id'];
    //         $ownerData = $this->allOwnersForPayroll[$i];

    //         if ($ownerData[$without]) {
    //             continue;
    //         }

    //         if ($ownerData['renta_nat']) {
    //             foreach ($ownerData['renta_nat'] as $renta_type_id => $owner_renta_value) {
    //                 if ('' != $renta_type_id) {
    //                     if (is_numeric($owner_renta_value)) {
    //                         $totalRentaNatTextForXlsArray[$renta_type_id] += $owner_renta_value;
    //                     }
    //                 }
    //             }
    //         }

    //         if ($ownerData['charged_renta_nat']) {
    //             foreach ($ownerData['charged_renta_nat'] as $renta_type_id => $owner_renta_value) {
    //                 if ('' != $renta_type_id) {
    //                     if (is_numeric($owner_renta_value)) {
    //                         $totalChargedRentaNatTextForXlsArray[$renta_type_id] += $owner_renta_value;
    //                     }
    //                 }
    //             }
    //         }

    //         $summaryArray[$ownerID]['owner_id'] = $ownerID;
    //         $summaryArray[$ownerID]['owner_type'] = $ownerData['owner_type'];
    //         $summaryArray[$ownerID]['owner_names'] = $ownerData['owner_names'];
    //         $summaryArray[$ownerID]['first_name'] = $ownerData['first_name'];
    //         $summaryArray[$ownerID]['surname'] = $ownerData['surname'];
    //         $summaryArray[$ownerID]['lastname'] = $ownerData['lastname'];
    //         $summaryArray[$ownerID]['phone'] = $ownerData['phone'];
    //         $summaryArray[$ownerID]['mobile'] = $ownerData['mobile'];
    //         $summaryArray[$ownerID]['rent_place'] = strlen($summaryArray[$ownerID]['rent_place']) > 0 && '-' !== $summaryArray[$ownerID]['rent_place']
    //             ? $summaryArray[$ownerID]['rent_place']
    //             : $ownerData['rent_place_name'];
    //         $summaryArray[$ownerID]['egn_eik'] = $ownerData['egn_eik'];
    //         $summaryArray[$ownerID]['full_rep_info'] = $ownerData['full_rep_info'];
    //         $summaryArray[$ownerID]['all_owner_area'] += is_numeric($ownerData['all_owner_area']) ? $ownerData['all_owner_area'] : 0;
    //         $summaryArray[$ownerID]['owner_area'] += is_numeric($ownerData['owner_area']) ? $ownerData['owner_area'] : 0;
    //         $summaryArray[$ownerID]['cultivated_area'] += is_numeric($ownerData['cultivated_area']) ? $ownerData['cultivated_area'] : 0;
    //         $summaryArray[$ownerID]['pu_area'] += is_numeric($ownerData['pu_area']) ? $ownerData['pu_area'] : 0;
    //         $summaryArray[$ownerID]['renta'] += is_numeric($ownerData['renta']) ? $ownerData['renta'] : 0;
    //         $summaryArray[$ownerID]['charged_renta'] += is_numeric($ownerData['charged_renta'])
    //             ? $ownerData['charged_renta']
    //             : (is_numeric($formattedValue = str_replace(',', '', $ownerData['charged_renta'])) ? $formattedValue : 0);
    //         $summaryArray[$ownerID]['paid_renta'] += is_numeric($ownerData['paid_renta']) ? $ownerData['paid_renta'] : 0;
    //         $summaryArray[$ownerID]['unpaid_renta'] += is_numeric($ownerData['unpaid_renta']) ? $ownerData['unpaid_renta'] : 0;
    //         $summaryArray[$ownerID]['total_by_renta'] += is_numeric($ownerData['total_by_renta_sum']) ? $ownerData['total_by_renta_sum'] : 0;
    //         $summaryArray[$ownerID]['over_paid'] += is_numeric($ownerData['over_paid']) ? $ownerData['over_paid'] : 0;

    //         $totalRentaByContract = [];
    //         foreach ($ownerData['plots_contracts_renta_down_grid'] as $contract_id => $rentaArr) {
    //             $totalRentaByContract[$contract_id]['renta'] = array_sum($rentaArr);
    //         }

    //         foreach ($ownerData['plots_contracts_charged_renta_down_grid'] as $contract_id => $chargedReantaArr) {
    //             $totalRentaByContract[$contract_id]['charged_renta'] = array_sum($chargedReantaArr);
    //         }

    //         foreach ($ownerData['paid_renta_contract'] as $contract_id => $paidRentaValue) {
    //             $totalRentaByContract[$contract_id]['paid_renta'] = $paidRentaValue;
    //         }

    //         foreach ($totalRentaByContract as $contractId => $rentaValues) {
    //             $renta = $rentaValues['renta'] ?? 0;
    //             $chargedRenta = $rentaValues['charged_renta'] ?? 0;
    //             $paidRenta = $rentaValues['paid_renta'] ?? 0;

    //             $unpaidRenta = ($renta + $chargedRenta) - $paidRenta;
    //             $ownerArray = [
    //                 'owner_id' => $ownerID,
    //                 'path' => $ownerData['path'] ?? null,
    //                 'contract_id' => $contractId,
    //                 'owner_area' => $ownerData['total_area_contract'][$contractId],
    //                 'charged_renta' => $chargedRenta,
    //                 'renta' => $renta,
    //                 'unpaid_renta' => $unpaidRenta,
    //                 'is_heritor' => $ownerData['is_heritor'],
    //                 'farming_id' => $ownerData['farming'],
    //             ];

    //             $summaryArray[$ownerID]['payment_data']['owner_array'][] = $ownerArray;
    //         }

    //         $summaryArray[$ownerID]['payment_data']['payment_type'] = 'owner_payments';
    //         $summaryArray[$ownerID]['payment_data']['year'] = $this->payrollExportFarmingYear;

    //         // Платена рента в лева чрез
    //         $paid_renta_by = $ownerData['paid_renta_by_arr'];
    //         $paid_renta_by_amount = $paid_renta_by['amount'];
    //         $paid_renta_by_nat_amount = $paid_renta_by['nat_amount'];

    //         if (!is_null($paid_renta_by_amount)) {
    //             $summaryArray[$ownerID]['paid_renta_by_arr']['amount'] += $paid_renta_by_amount;
    //         }
    //         if (!empty($paid_renta_by_nat_amount)) {
    //             foreach ($paid_renta_by_nat_amount as $key => $value) {
    //                 $summaryArray[$ownerID]['paid_renta_by_arr']['nat_amount'][$key] += $value;
    //             }
    //         }

    //         // Платена рента в натура чрез
    //         $paid_renta_nat_by = $ownerData['paid_renta_nat_by_arr'];
    //         $paid_renta_nat_by_amount = $paid_renta_nat_by['amount'];
    //         $paid_renta_nat_by_nat_amount = $paid_renta_nat_by['nat_amount'];

    //         if (!is_null($paid_renta_nat_by_amount)) {
    //             $summaryArray[$ownerID]['paid_renta_nat_by_arr']['amount'] += $paid_renta_nat_by_amount;
    //         }
    //         if (!empty($paid_renta_nat_by_nat_amount)) {
    //             foreach ($paid_renta_nat_by_nat_amount as $key => $value) {
    //                 $summaryArray[$ownerID]['paid_renta_nat_by_arr']['nat_amount'][$key] += $value;
    //             }
    //         }

    //         // Платена рента в натура
    //         $summaryArray[$ownerID]['paid_renta_nat_arr'] = [];
    //         if (!empty($ownerData['paid_renta_nat_arr'])) {
    //             foreach ($ownerData['paid_renta_nat_arr'] as $key => $value) {
    //                 $summaryArray[$ownerID]['paid_renta_nat_arr'][$key] += $value;
    //             }
    //         }

    //         // Платена рента в натура детайлно
    //         $all_paid_renta_nat_by_detailed_quantity_arr = $ownerData['all_paid_renta_nat_by_detailed_quantity_arr'];
    //         $all_paid_renta_nat_by_detailed_unit_value_arr = $ownerData['all_paid_renta_nat_by_detailed_unit_value_arr'];
    //         if (!empty($all_paid_renta_nat_by_detailed_quantity_arr)) {
    //             foreach ($all_paid_renta_nat_by_detailed_quantity_arr as $key => $value) {
    //                 $summaryArray[$ownerID]['all_paid_renta_nat_by_detailed_quantity_arr'][$key] += $value;
    //             }
    //             foreach ($all_paid_renta_nat_by_detailed_unit_value_arr as $key => $value) {
    //                 $summaryArray[$ownerID]['all_paid_renta_nat_by_detailed_unit_value_arr'][$key] = $value;
    //             }
    //         }

    //         // Остатък рента в натура
    //         $unpaid_renta_nat = $ownerData['unpaid_renta_nat_arr'];
    //         if (!empty($unpaid_renta_nat)) {
    //             foreach ($unpaid_renta_nat as $key => $value) {
    //                 $summaryArray[$ownerID]['unpaid_renta_nat'][$key] += $value;
    //             }
    //         }

    //         // Остатък рента в натура в ед.ст.
    //         $unpaid_renta_nat_unit_value = $ownerData['unpaid_renta_nat_unit_value_arr'];
    //         if (!empty($unpaid_renta_nat_unit_value)) {
    //             foreach ($unpaid_renta_nat_unit_value as $key => $value) {
    //                 $summaryArray[$ownerID]['unpaid_renta_nat_unit_value_arr'][$key] += $value;
    //             }
    //         }

    //         // Общо рента в натура
    //         $total_by_renta_nat = $ownerData['total_by_renta_nat_arr'];
    //         if (!empty($total_by_renta_nat)) {
    //             foreach ($total_by_renta_nat as $key => $value) {
    //                 $summaryArray[$ownerID]['total_by_renta_nat_arr_last'][$key] += $value;
    //             }
    //         }

    //         // Надплатена рента в натура
    //         $over_paid_renta_nat_arr = $ownerData['overpaid_renta_nat_arr'];
    //         if (!empty($over_paid_renta_nat_arr)) {
    //             foreach ($over_paid_renta_nat_arr as $key => $value) {
    //                 $summaryArray[$ownerID]['overpaid_renta_nat_arr'][$key] += $value;
    //             }
    //         }

    //         // Рента в натура по договор
    //         if ('-' != $ownerData['renta_nat_text']) {
    //             $summaryArray[$ownerID]['renta_nat_text'] .= $ownerData['renta_nat_text'] . ',<br/>';
    //         }

    //         // '[Рента в натура] X [тип рента]' по договор
    //         if ('-' != $ownerData['renta_nat_with_type']) {
    //             $summaryArray[$ownerID]['renta_nat_with_type'] .= $ownerData['renta_nat_with_type'];
    //         }

    //         // Начислена рента в натура
    //         if ('-' != $ownerData['charged_renta_nat_text']) {
    //             $summaryArray[$ownerID]['charged_renta_nat_text'] .= $ownerData['charged_renta_nat_text'] . ',<br/>';
    //         }

    //         // Договори
    //         $summaryArray[$ownerID]['c_num_array'] = array_unique([...($summaryArray[$ownerID]['c_num_array'] ?? []), ...($ownerData['c_num_array'] ?? [])]);
    //         $summaryArray[$ownerID]['c_num'] = implode(', ', $summaryArray[$ownerID]['c_num_array']);

    //         if (!is_array($summaryArray[$ownerID]['sv_num_array'])) {
    //             $summaryArray[$ownerID]['sv_num_array'] = [];
    //         }

    //         if (!is_array($ownerData['sv_num_array']) || null == $ownerData['sv_num_array']) {
    //             $ownerData['sv_num_array'] = [];
    //         }

    //         $summaryArray[$ownerID]['sv_num_array'] = array_unique(array_filter(array_merge($summaryArray[$ownerID]['sv_num_array'], $ownerData['sv_num_array']), function ($var) {
    //             if (!empty(trim($var, '"'))) {
    //                 return $var;
    //             }
    //         }));

    //         $summaryArray[$ownerID]['sv_num'] = implode(', ', $summaryArray[$ownerID]['sv_num_array']);

    //         // Имоти
    //         $summaryArray[$ownerID]['plots_contracts_area_array'] = array_unique([...($summaryArray[$ownerID]['plots_contracts_area_array'] ?? []), ...($ownerData['kad_idents_array'] ?? [])]);

    //         // Представители
    //         $summaryArray[$ownerID]['rep_names_array'] = array_unique([...($summaryArray[$ownerID]['rep_names_array'] ?? []), ...($ownerData['rep_names_array'] ?? [])]);
    //         $summaryArray[$ownerID]['rep_names'] = implode(', ', $summaryArray[$ownerID]['rep_names_array']);

    //         // IBAN
    //         $summaryArray[$ownerID]['iban'] = $ownerData['iban'] ?? '';
    //     }

    //     // to normalize indexes
    //     $summaryArray = array_merge($summaryArray);

    //     $totalAreaForXls = 0;
    //     $totalCultivatedArea = 0;
    //     $totalPersonalUseAreaForXls = 0;
    //     $totalRentaForXls = 0;
    //     $totalChargedRentaForXls = 0;
    //     $totalPaidRentaForXls = 0;
    //     $totalUnpaidRentaForXls = 0;
    //     $totalOverpaidRentaForXls = 0;
    //     $totalByRentaForXls = 0;
    //     $totalPaidRentaByForXls = 0;
    //     $totalPaidRentaByNatForXlsArray = [];
    //     $totalPaidNatForXlsArray = [];
    //     $totalPaidNatByForXlsArray = [];
    //     $totalPaidNatByForXls = 0;
    //     $totalPaidNatDetailedForXlsArray = [];
    //     $unitValuesArray = [];
    //     $totalUnpaidRentaNatForXlsArray = [];
    //     $totalUnpaidRentaNatDetailValueXls = 0;
    //     $totalOverpaidRentaNatForXlsArray = [];
    //     $totalPaidRentaNatForXlsArray = [];

    //     // calculate all renta_nat
    //     $countSummaryArray = count($summaryArray);
    //     for ($i = 0; $i < $countSummaryArray; $i++) {
    //         // help arrays
    //         $tempRentaNat = [];
    //         $tempChargedRentaNat = [];
    //         $tmp_renta_nat = [];
    //         $tmp_charged_renta = [];

    //         $totalAreaForXls += $summaryArray[$i]['area'];
    //         $totalCultivatedArea += $summaryArray[$i]['cultivated_area'];
    //         $totalPersonalUseAreaForXls += $summaryArray[$i]['pu_area'];
    //         $totalRentaForXls += $summaryArray[$i]['renta'];
    //         $totalChargedRentaForXls += $summaryArray[$i]['charged_renta'];
    //         $totalPaidRentaForXls += $summaryArray[$i]['paid_renta'];
    //         $totalUnpaidRentaForXls += $summaryArray[$i]['unpaid_renta'];
    //         $totalOverpaidRentaForXls += $summaryArray[$i]['over_paid'];
    //         $totalByRentaForXls += $summaryArray[$i]['total_by_renta'];

    //         $summaryArray[$i]['paid_renta'] = number_format($summaryArray[$i]['paid_renta'], 2, '.', '');
    //         $summaryArray[$i]['unpaid_renta'] = number_format($summaryArray[$i]['unpaid_renta'], 2, '.', '');
    //         $summaryArray[$i]['over_paid'] = number_format($summaryArray[$i]['over_paid'], 2, '.', '');

    //         // Рента в натура по договор
    //         if ($summaryArray[$i]['renta_nat_with_type']) {
    //             $rentaNat = explode('<br>', $summaryArray[$i]['renta_nat_with_type']);
    //             $rentaNat = array_filter($rentaNat);
    //             $rentaNatCount = count($rentaNat);
    //             for ($j = 0; $j < $rentaNatCount; $j++) {
    //                 $tmp_renta_nat = explode(' X ', $rentaNat[$j]);
    //                 $tempRentaNat[$tmp_renta_nat[1]] += $tmp_renta_nat[0];
    //             }

    //             unset($summaryArray[$i]['renta_nat_text']);
    //             foreach ($tempRentaNat as $renta_nat_type => $renta_nat) {
    //                 if ($renta_nat > 0) {
    //                     $summaryArray[$i]['renta_nat_text'] .= number_format($renta_nat, 3, '.', '') . ' X ' . $renta_nat_type . ',<br/>';
    //                     // Добавяне на всички уникални натури по договор в помощния масив
    //                     // и добавяне на нови колони за всяка отделна рента
    //                     // за всеки отделен резултат във формат
    //                     // renta_nat_text_Натура(мерна единица)
    //                     $tmpRentaNatType = trim($renta_nat_type, ',');
    //                     $summaryArray[$i]['renta_nat_text_' . $tmpRentaNatType] = number_format($renta_nat, 3, '.', '');
    //                     if (!in_array($tmpRentaNatType, $this->uniqueNatura['renta_nat_text'])) {
    //                         array_push($this->uniqueNatura['renta_nat_text'], $tmpRentaNatType);
    //                     }
    //                 }
    //             }
    //         } else {
    //             $summaryArray[$i]['renta_nat_text'] = '-';
    //         }

    //         // Начислена рента в натура
    //         if ($summaryArray[$i]['charged_renta_nat_text']) {
    //             $chargedRentaNat = explode('<br/>', $summaryArray[$i]['charged_renta_nat_text']);
    //             $chargedRentaNat = array_filter($chargedRentaNat);
    //             $chargedRentaNatCount = count($chargedRentaNat);
    //             for ($j = 0; $j < $chargedRentaNatCount; $j++) {
    //                 $tmp_charged_renta = explode(' X ', $chargedRentaNat[$j]);
    //                 $tempChargedRentaNat[$tmp_charged_renta[1]] += is_numeric($tmp_charged_renta[0]) ? $tmp_charged_renta[0] : 0;
    //             }

    //             unset($summaryArray[$i]['charged_renta_nat_text']);
    //             foreach ($tempChargedRentaNat as $charge_renta_nat_type => $charget_renta_nat) {
    //                 if ('' != $charge_renta_nat_type || 0 != $charget_renta_nat || null != $charget_renta_nat) {
    //                     $summaryArray[$i]['charged_renta_nat_text'] .= number_format($charget_renta_nat, 3, '.', '') . ' X ' . $charge_renta_nat_type . ',<br/>';

    //                     $tmpRentaNatType = trim($charge_renta_nat_type, ',');
    //                     // Добавяне на всички уникални начислени натури по договор в помощния масив
    //                     // и добавяне на нови колони за всяка отделна натура
    //                     // за всеки отделен резултат във формат
    //                     // charged_renta_nat_text_Натура(мерна единица)
    //                     $summaryArray[$i]['charged_renta_nat_text_' . $tmpRentaNatType] = number_format($charget_renta_nat, 3, '.', '');
    //                     if (!in_array($tmpRentaNatType, $this->uniqueNatura['charged_renta_nat_text'])) {
    //                         array_push($this->uniqueNatura['charged_renta_nat_text'], $tmpRentaNatType);
    //                     }
    //                 }
    //             }
    //             if (empty($summaryArray[$i]['charged_renta_nat_text'])) {
    //                 $summaryArray[$i]['charged_renta_nat_text'] = '-';
    //             }
    //         } else {
    //             $summaryArray[$i]['charged_renta_nat_text'] = '-';
    //         }

    //         // Платена рента в натура
    //         if ($summaryArray[$i]['paid_renta_nat_arr']) {
    //             foreach ($summaryArray[$i]['paid_renta_nat_arr'] as $key => $value) {
    //                 if ('' == $key || 0 == $value) {
    //                     continue;
    //                 }
    //                 $summaryArray[$i]['paid_renta_nat'][] = number_format($value, 3, '.', '') . ' X ' . $this->renta_types[$key];
    //                 $totalPaidNatForXlsArray[$key] += $value;

    //                 $tmpRentaNatType = $this->renta_types[$key];
    //                 // Добавяне на всички уникални изплатени натури в помощния масив
    //                 // и добавяне на нови колони за всяка отделна натура
    //                 // за всеки отделен резултат във формат
    //                 // paid_renta_nat_Натура(мерна единица)
    //                 $summaryArray[$i]['paid_renta_nat_' . $tmpRentaNatType] = number_format($value, 3, '.', '');
    //                 if (!in_array($tmpRentaNatType, $this->uniqueNatura['paid_renta_nat'])) {
    //                     array_push($this->uniqueNatura['paid_renta_nat'], $tmpRentaNatType);
    //                 }
    //             }
    //             if (empty($summaryArray[$i]['paid_renta_nat'])) {
    //                 $summaryArray[$i]['paid_renta_nat'] = '-';
    //             } else {
    //                 $summaryArray[$i]['paid_renta_nat'] = implode(', ', $summaryArray[$i]['paid_renta_nat']);
    //             }
    //         } else {
    //             $summaryArray[$i]['paid_renta_nat'] = '-';
    //         }

    //         // Платена рента в лева чрез
    //         $paid_renta_by = $summaryArray[$i]['paid_renta_by_arr'];
    //         $paid_renta_by_amount = $paid_renta_by['amount'];

    //         $totalPaidRentaByForXls += $paid_renta_by_amount;

    //         $tmp_paid_renta_by = [];
    //         if (!is_null($paid_renta_by_amount) && '-' !== $paid_renta_by_amount) {
    //             $tmp_paid_renta_by[] = number_format($paid_renta_by_amount, 2, '.', '') . ' лв.';
    //         }
    //         $paid_renta_by_nat_amount = $paid_renta_by['nat_amount'];

    //         if (!empty($paid_renta_by_nat_amount)) {
    //             foreach ($paid_renta_by_nat_amount as $key => $rentaNat) {
    //                 if ('' == $key || '-' == $rentaNat) {
    //                     continue;
    //                 }
    //                 $rentaNat = '-' != $rentaNat ? number_format($rentaNat, 3, '.', '') : '-';
    //                 $totalPaidRentaByNatForXlsArray[$key] += '-' != $rentaNat ? $rentaNat : 0;
    //                 $tmp_paid_renta_by[] .= $rentaNat . ' X ' . $this->renta_types[$key];

    //                 $tmpRentaNatType = $this->renta_types[$key];
    //                 // Добавяне на всички уникални изплатени натури, при изплащане на рента, в помощния масив
    //                 // и добавяне на нови колони за всяка отделна натура
    //                 // за всеки отделен резултат във формат
    //                 // paid_renta_by_Натура(мерна единица)
    //                 $summaryArray[$i]['paid_renta_by_' . $tmpRentaNatType] = $rentaNat;
    //                 if (!in_array($tmpRentaNatType, $this->uniqueNatura['paid_renta_by'])) {
    //                     array_push($this->uniqueNatura['paid_renta_by'], $tmpRentaNatType);
    //                 }
    //             }
    //         }
    //         if (empty($tmp_paid_renta_by)) {
    //             $tmp_paid_renta_by[] = '-';
    //         }

    //         // Платена рента в натура чрез
    //         $paid_renta_nat_by = $summaryArray[$i]['paid_renta_nat_by_arr'];
    //         $paid_renta_nat_by_amount = $paid_renta_nat_by['amount'];

    //         $tmp_paid_renta_nat_by = [];
    //         if (!is_null($paid_renta_nat_by_amount) && '-' !== $paid_renta_nat_by_amount) {
    //             $totalPaidNatByForXls += $paid_renta_nat_by_amount;
    //             $tmp_paid_renta_nat_by[] = number_format($paid_renta_nat_by_amount, 2, '.', '') . ' лв.';
    //         }
    //         $paid_renta_nat_by_nat_amount = $paid_renta_nat_by['nat_amount'];
    //         if (!empty($paid_renta_nat_by_nat_amount)) {
    //             foreach ($paid_renta_nat_by_nat_amount as $key => $rentaNat) {
    //                 if ('' == $key || '-' == $rentaNat) {
    //                     continue;
    //                 }
    //                 $rentaNat = '-' != $rentaNat ? number_format($rentaNat, 3, '.', '') : '-';
    //                 $totalPaidNatByForXlsArray[$key] += '-' != $rentaNat ? $rentaNat : 0;
    //                 $tmp_paid_renta_nat_by[] = $rentaNat . ' X ' . $this->renta_types[$key];

    //                 $tmpRentaNatType = $this->renta_types[$key];

    //                 // Добавяне на всички уникални изплатени натури, при изплащане на натура, в помощния масив
    //                 // и добавяне на нови колони за всяка отделна натура
    //                 // за всеки отделен резултат във формат
    //                 // paid_renta_nat_by_Натура(мерна единица)
    //                 //
    //                 $summaryArray[$i]['paid_renta_nat_by_' . $tmpRentaNatType] = $rentaNat;
    //                 if (!in_array($tmpRentaNatType, $this->uniqueNatura['paid_renta_nat_by'])) {
    //                     array_push($this->uniqueNatura['paid_renta_nat_by'], $tmpRentaNatType);
    //                 }
    //             }
    //         }
    //         if (empty($tmp_paid_renta_nat_by)) {
    //             $tmp_paid_renta_nat_by[] = '-';
    //         }

    //         // Платена рента в натура детайлно
    //         $all_paid_renta_nat_by_detailed_quantity_arr = $summaryArray[$i]['all_paid_renta_nat_by_detailed_quantity_arr'];
    //         $all_paid_renta_nat_by_detailed_unit_value_arr = $summaryArray[$i]['all_paid_renta_nat_by_detailed_unit_value_arr'];
    //         $tmp_paid_renta_nat_by_detailed = [];
    //         if (!empty($all_paid_renta_nat_by_detailed_quantity_arr)) {
    //             foreach ($all_paid_renta_nat_by_detailed_quantity_arr as $key => $rentaNat) {
    //                 if ('' == $key || '-' == $rentaNat) {
    //                     continue;
    //                 }
    //                 $rentaNat = '-' != $rentaNat ? number_format($rentaNat, 3, '.', '') : '-';
    //                 $unitValue = $all_paid_renta_nat_by_detailed_unit_value_arr[$key];

    //                 $totalPaidNatDetailedForXlsArray[$key] += $rentaNat;
    //                 $unitValuesArray[$key] = $unitValue;
    //                 $tmp_paid_renta_nat_by_detailed[] = $rentaNat . ' X ' . $this->renta_types[$key] . ' X ' . $unitValue . ' ед.ст.';
    //             }
    //         } else {
    //             $tmp_paid_renta_nat_by_detailed[] = '-';
    //         }

    //         // Остатък рента в натура
    //         $unpaid_renta_nat = $summaryArray[$i]['unpaid_renta_nat'];
    //         $tmp_unpaid_renta_nat = [];
    //         if (!empty($unpaid_renta_nat)) {
    //             foreach ($unpaid_renta_nat as $key => $rentaNat) {
    //                 if ('' == $key || '-' == $rentaNat) {
    //                     continue;
    //                 }

    //                 $rentaNat = '-' != $rentaNat ? number_format($rentaNat, 3, '.', '') : '-';
    //                 $totalUnpaidRentaNatForXlsArray[$key] += $rentaNat;
    //                 $tmp_unpaid_renta_nat[] = $rentaNat . ' X ' . $this->renta_types[$key];

    //                 $tmpRentaNatType = $this->renta_types[$key];

    //                 // Добавяне на всички уникални неизплатени натури
    //                 // и добавяне на нови колони за всяка отделна натура
    //                 // за всеки отделен резултат във формат
    //                 // unpaid_renta_Натура(мерна единица)
    //                 $summaryArray[$i]['unpaid_renta_nat_' . $tmpRentaNatType] = $rentaNat;
    //                 if (!in_array($tmpRentaNatType, $this->uniqueNatura['unpaid_renta_nat'])) {
    //                     array_push($this->uniqueNatura['unpaid_renta_nat'], $tmpRentaNatType);
    //                 }
    //             }
    //         } else {
    //             $tmp_unpaid_renta_nat[] = '-';
    //         }
    //         if (empty($tmp_unpaid_renta_nat)) {
    //             $tmp_unpaid_renta_nat[] = '-';
    //         }

    //         // Платерена рента в натура ед.ст.
    //         $unpaid_renta_nat_unit_value = $summaryArray[$i]['unpaid_renta_nat_unit_value_arr'];
    //         $tmp_unpaid_renta_nat_unit_value = [];
    //         if (!empty($unpaid_renta_nat_unit_value)) {
    //             foreach ($unpaid_renta_nat_unit_value as $key => $rentaNat) {
    //                 if ('' == $key) {
    //                     continue;
    //                 }
    //                 $rentaNat = '-' != $rentaNat ? number_format($rentaNat, 2, '.', '') : '-';
    //                 $totalUnpaidRentaNatDetailValueXls += is_numeric($rentaNat) ? $rentaNat : 0;
    //                 $tmp_unpaid_renta_nat_unit_value[] = $rentaNat;

    //                 $tmpRentaNatType = $this->renta_types[$key];
    //                 // Добавяне на всички уникални неизплатени натури, умножени по единичната им стойност
    //                 // и добавяне на нови колони за всяка отделна натура
    //                 // за всеки отделен резултат във формат
    //                 // unpaid_renta_nat_unit_value_Натура(мерна единица)
    //                 $summaryArray[$i]['unpaid_renta_nat_unit_value_' . $tmpRentaNatType] = $rentaNat;
    //                 if (!in_array($tmpRentaNatType, $this->uniqueNatura['unpaid_renta_nat_unit_value'])) {
    //                     array_push($this->uniqueNatura['unpaid_renta_nat_unit_value'], $tmpRentaNatType);
    //                 }
    //             }
    //         } else {
    //             $tmp_unpaid_renta_nat_unit_value[] = '-';
    //         }

    //         // Общо платена рента в натура
    //         $total_by_renta_nat = $summaryArray[$i]['total_by_renta_nat_arr_last'];
    //         $tmp_total_by_renta_nat = [];
    //         if (!empty($total_by_renta_nat)) {
    //             foreach ($total_by_renta_nat as $key => $rentaNat) {
    //                 if ('' == $key || '-' == $rentaNat) {
    //                     continue;
    //                 }
    //                 $rentaNat = '-' != $rentaNat ? number_format($rentaNat, 3, '.', '') : '-';
    //                 $totalPaidRentaNatForXlsArray[$key] += $rentaNat;
    //                 $tmp_total_by_renta_nat[] = $rentaNat . ' X ' . $this->renta_types[$key];
    //             }
    //         } else {
    //             $tmp_total_by_renta_nat[] = '-';
    //         }
    //         if (empty($tmp_total_by_renta_nat)) {
    //             $tmp_total_by_renta_nat[] = '-';
    //         }

    //         // Надплатена рента в натура
    //         $over_paid_renta_nat_arr = $summaryArray[$i]['overpaid_renta_nat_arr'];
    //         $tmp_over_paid_renta_nat = [];
    //         if (!empty($over_paid_renta_nat_arr)) {
    //             foreach ($over_paid_renta_nat_arr as $key => $rentaNat) {
    //                 if ('' == $key || '-' == $rentaNat) {
    //                     continue;
    //                 }
    //                 $rentaNat = '-' != $rentaNat ? number_format($rentaNat, 3, '.', '') : '-';
    //                 $totalOverpaidRentaNatForXlsArray[$key] += $rentaNat;
    //                 $tmp_over_paid_renta_nat[] = $rentaNat . ' X ' . $this->renta_types[$key];

    //                 $tmpRentaNatType = $this->renta_types[$key];
    //                 // Добавяне на всички уникални надплатени натури
    //                 // и добавяне на нови колони за всяка отделна натура
    //                 // за всеки отделен резултат във формат
    //                 // over_paid_renta_nat_Натура(мерна единица)
    //                 $summaryArray[$i]['over_paid_renta_nat_' . $tmpRentaNatType] = $rentaNat;
    //                 if (!in_array($tmpRentaNatType, $this->uniqueNatura['over_paid_renta_nat'])) {
    //                     array_push($this->uniqueNatura['over_paid_renta_nat'], $tmpRentaNatType);
    //                 }
    //             }
    //         } else {
    //             $tmp_over_paid_renta_nat[] = '-';
    //         }
    //         if (empty($tmp_over_paid_renta_nat)) {
    //             $tmp_over_paid_renta_nat[] = '-';
    //         }

    //         // Имоти
    //         $plots_contracts_area_array = $summaryArray[$i]['plots_contracts_area_array'];
    //         if (!empty($plots_contracts_area_array)) {
    //             $plots_contracts_area_array = array_unique($plots_contracts_area_array);
    //         } else {
    //             $plots_contracts_area_array[] = '-';
    //         }

    //         // prepare data for the table
    //         $summaryArray[$i]['number'] = $i + 1;
    //         $summaryArray[$i]['area'] = number_format($summaryArray[$i]['area'], 3, '.', '');
    //         $summaryArray[$i]['cultivated_area'] = number_format($summaryArray[$i]['cultivated_area'], 3, '.', '');
    //         $summaryArray[$i]['pu_area'] = number_format($summaryArray[$i]['pu_area'], 3, '.', '');
    //         $summaryArray[$i]['renta'] = number_format($summaryArray[$i]['renta'], 2, '.', '');
    //         $summaryArray[$i]['charged_renta'] = number_format($summaryArray[$i]['charged_renta'], 2, '.', '');
    //         $summaryArray[$i]['total_by_renta'] = number_format($summaryArray[$i]['total_by_renta'], 2, '.', '');
    //         $summaryArray[$i]['renta_nat_text'] = trim($summaryArray[$i]['renta_nat_text'], ',<br/>');
    //         $summaryArray[$i]['charged_renta_nat_text'] = trim($summaryArray[$i]['charged_renta_nat_text'], ',<br/>');
    //         $summaryArray[$i]['paid_renta_by'] = implode(', ', $tmp_paid_renta_by);
    //         $summaryArray[$i]['paid_renta_nat_by'] = implode(', ', $tmp_paid_renta_nat_by);
    //         $summaryArray[$i]['paid_renta_nat_by_detailed'] = implode(', ', $tmp_paid_renta_nat_by_detailed);
    //         $summaryArray[$i]['unpaid_renta_nat'] = implode(', ', $tmp_unpaid_renta_nat);
    //         $summaryArray[$i]['unpaid_renta_nat_unit_value'] = implode(', ', $tmp_unpaid_renta_nat_unit_value);
    //         $summaryArray[$i]['total_by_renta_nat'] = implode(', ', $tmp_total_by_renta_nat);
    //         $summaryArray[$i]['over_paid_renta_nat'] = implode(', ', $tmp_over_paid_renta_nat);
    //         $summaryArray[$i]['plots_name'] = implode(', ', $plots_contracts_area_array);
    //     }

    //     // Подготвяне на данните за общо за колона: Рента в натура по договор (общо)
    //     ksort($totalRentaNatTextForXlsArray);
    //     $totalRentaNatTextForXls = '';
    //     foreach ($totalRentaNatTextForXlsArray as $key => $value) {
    //         $totalRentaNatTextForXls .= number_format($value, 3, '.', '') . ' X ' . $this->renta_types[$key] . '</br>';
    //     }

    //     // Подготвяне на данните за общо за колона: Начислена рента в натура (общо)
    //     ksort($totalChargedRentaNatTextForXlsArray);
    //     $totalChargedRentaNatTextForXls = '';
    //     foreach ($totalChargedRentaNatTextForXlsArray as $key => $value) {
    //         if (0 != $value) {
    //             $totalChargedRentaNatTextForXls .= number_format($value, 3, '.', '') . ' X ' . $this->renta_types[$key] . '</br>';
    //         }
    //     }
    //     // Подготвяне на данните за общо за колона: Платена рента в лева чрез
    //     ksort($totalPaidRentaByNatForXlsArray);
    //     $totalPaidRentaByNatForXls = '';
    //     foreach ($totalPaidRentaByNatForXlsArray as $key => $value) {
    //         if (0 != $value) {
    //             $totalPaidRentaByNatForXls .= number_format($value, 3, '.', '') . ' X ' . $this->renta_types[$key] . '</br>';
    //         }
    //     }
    //     $totalPaidRentaByForXls = number_format($totalPaidRentaByForXls, 2, '.', '') . ' лв.</br>' . $totalPaidRentaByNatForXls;

    //     // Подготвяне на данните за общо за колона: Платена рента в натура (общо)
    //     ksort($totalPaidNatForXlsArray);
    //     $totalPaidNatForXls = '';
    //     foreach ($totalPaidNatForXlsArray as $key => $value) {
    //         if (0 != $value) {
    //             $totalPaidNatForXls .= number_format($value, 3, '.', '') . ' X ' . $this->renta_types[$key] . '</br>';
    //         }
    //     }

    //     // Подготвяне на данните за общо за колона: Платена рента в натура детайлно
    //     ksort($totalPaidNatDetailedForXlsArray);
    //     $totalPaidNatDetailedForXls = '';
    //     foreach ($totalPaidNatDetailedForXlsArray as $key => $value) {
    //         if (0 != $value) {
    //             $totalPaidNatDetailedForXls .= number_format($value, 3, '.', '') . ' X ' . $this->renta_types[$key] . ' X ' . $unitValuesArray[$key] . ' ед.ст.</br>';
    //         }
    //     }

    //     // Подготвяне на данните за общо за колона: Остатък в натура
    //     ksort($totalUnpaidRentaNatForXlsArray);
    //     $totalUnpaidRentaNatForXls = '';
    //     foreach ($totalUnpaidRentaNatForXlsArray as $key => $value) {
    //         if (0 != $value) {
    //             $totalUnpaidRentaNatForXls .= number_format($value, 3, '.', '') . ' X ' . $this->renta_types[$key] . '</br>';
    //         }
    //     }

    //     // Подготвяне на данните за общо за колона: Надплатена рента в натура
    //     ksort($totalOverpaidRentaNatForXlsArray);
    //     $totalOverpaidRentaNatForXls = '';
    //     foreach ($totalOverpaidRentaNatForXlsArray as $key => $value) {
    //         if (0 != $value) {
    //             $totalOverpaidRentaNatForXls .= number_format($value, 3, '.', '') . ' X ' . $this->renta_types[$key] . '</br>';
    //         }
    //     }

    //     // Подготвяне на данните за общо за колона: Общо платена рента в натура
    //     ksort($totalPaidRentaNatForXlsArray);
    //     $totalPaidRentaNatForXls = '';
    //     foreach ($totalPaidRentaNatForXlsArray as $key => $value) {
    //         if (0 != $value) {
    //             $totalPaidRentaNatForXls .= number_format($value, 3, '.', '') . ' X ' . $this->renta_types[$key] . '</br>';
    //         }
    //     }

    //     // Подготвяне на данните за общо за колона: Платена рента в натура чрез
    //     ksort($totalPaidNatByForXlsArray);
    //     $totalPaidNatByForXlsRents = '';
    //     foreach ($totalPaidNatByForXlsArray as $key => $value) {
    //         if (0 != $value) {
    //             $totalPaidNatByForXlsRents .= number_format($value, 3, '.', '') . ' X ' . $this->renta_types[$key] . '</br>';
    //         }
    //     }
    //     $totalPaidNatByForXls = number_format($totalPaidNatByForXls, 2, '.', '') . ' лв.</br>' . $totalPaidNatByForXlsRents;

    //     /*
    //      * Рента в лева по договор(сума)   - $totalRentaForXls
    //      * Обща площ за рента              - $totalAreaForXls
    //      * Начислена рента в лева (сума)   - $totalChargedRentaForXls
    //      * Рента в натура по договор(общо) - $totalRentaNatTextForXls
    //      * Начислена рента в натура(общо)  - $totalChargedRentaNatTextForXls
    //      * Платена рента в лева(сума)      - $totalPaidRentaForXls
    //      * Платена рента в лева чрез       - $totalPaidRentaByForXls
    //      * Платена рента в натура(общо)    - $totalPaidNatForXls
    //      * Платена рента в натура чрез     - $totalPaidNatByForXls
    //      * Платена рента в натура детайлно - $totalPaidNatDetailedForXls
    //      * Остатък в лева                  - $totalUnpaidRentaForXls
    //      * Остатък в натура                - $totalUnpaidRentaNatForXls
    //      * Остатък в натура по ед.ст.      - $totalUnpaidRentaNatDetailValueXls
    //      * Надплатена рента в лева         - $totalOverpaidRentaForXls
    //      * Надплатена рента в натура       - $totalOverpaidRentaNatForXls
    //      * Общо платена рента в лева       - $totalByRentaForXls
    //      * Общо платена рента в натура     - $totalPaidRentaNatForXls
    //      */

    //     $footerArray = [
    //         'c_num' => '',
    //         'plots_name' => '',
    //         'rep_names' => '',
    //         'number' => '',
    //         'owner_names' => '',
    //         'egn_eik' => 'ОБЩО:',
    //         'area' => number_format($totalAreaForXls, 3, '.', ''),
    //         'cultivated_area' => number_format($totalCultivatedArea, 3, '.', ''),
    //         'pu_area' => number_format($totalPersonalUseAreaForXls, 3, '.', ''),
    //         'renta' => number_format($totalRentaForXls, 2, '.', ''),
    //         'charged_renta' => number_format($totalChargedRentaForXls, 2, '.', ''),
    //         'renta_nat_text' => $totalRentaNatTextForXls,
    //         'charged_renta_nat_text' => $totalChargedRentaNatTextForXls,
    //         'paid_renta' => number_format($totalPaidRentaForXls, 2, '.', ''),
    //         'paid_renta_by' => $totalPaidRentaByForXls,
    //         'paid_renta_nat' => $totalPaidNatForXls,
    //         'paid_renta_nat_by' => $totalPaidNatByForXls,
    //         'paid_renta_nat_by_detailed' => $totalPaidNatDetailedForXls,
    //         'unpaid_renta' => number_format($totalUnpaidRentaForXls, 2, '.', ''),
    //         'unpaid_renta_nat' => $totalUnpaidRentaNatForXls,
    //         'unpaid_renta_nat_unit_value' => number_format($totalUnpaidRentaNatDetailValueXls, 2, '.', ''),
    //         'over_paid' => number_format($totalOverpaidRentaForXls, 2, '.', ''),
    //         'over_paid_renta_nat' => $totalOverpaidRentaNatForXls,
    //         'total_by_renta' => number_format($totalByRentaForXls, 2, '.', ''),
    //         'total_by_renta_nat' => $totalPaidRentaNatForXls,
    //     ];

    //     $this->footerArray = $footerArray;

    //     return $summaryArray;
    // }

    /**
     * Returns the data for detailed payroll(owner-contract-plot gouped data for the living people).
     *
     * @param PayrollGrid $PayrollGrid
     * @param array $data
     *
     * @deprecated This method is used for payroll export of type 'Разделно (без починалите)' which is hidden in FE [GPS-4765]
     *
     * @return array
     */
    // private function getDetailedPayrollData($PayrollGrid, $data)
    // {
    //     $detailedArray = [];
    //     $dataCount = count($data);
    //     for ($i = 0; $i < $dataCount; $i++) {
    //         if ($data[$i]['is_dead']) {
    //             continue;
    //         }

    //         $ownerID = $data[$i]['owner_id'];
    //         $ownerPath = $data[$i]['path'];

    //         unset($_POST['sort']);
    //         $_POST['owner_id'] = $ownerID;
    //         $_POST['path'] = $ownerPath;
    //         $_POST['root_id'] = $data[$i]['root_id'];

    //         $allRenta['plots_contracts_renta_down_grid'] = $data[$i]['plots_contracts_renta_down_grid'];
    //         $allRenta['plots_contracts_charged_renta_down_grid'] = $data[$i]['plots_contracts_charged_renta_down_grid'];
    //         $allRenta['plots_contracts_renta_nat_down_grid'] = $data[$i]['plots_contracts_renta_nat_down_grid'];
    //         $allRenta['plots_contracts_charged_renta_nat_down_grid'] = $data[$i]['plots_contracts_charged_renta_nat_down_grid'];

    //         $paymentsController = new PaymentsController($this->User->Database);
    //         $ownerContractPlotsResults = $paymentsController->getOwnerPayroll($data[$i]['farming_year'], $data[$i]['owner_id'], $data[$i]['path']);

    //         $detailedArray = array_merge($detailedArray, $ownerContractPlotsResults);
    //     }

    //     $countDetailedArray = count($detailedArray);
    //     for ($i = 0; $i < $countDetailedArray; $i++) {
    //         $detailedArray[$i]['plots_name'] = $detailedArray[$i]['kad_ident'];

    //         $separatedRentaNat = explode('<br>', $this->strrtrim('<br>', $detailedArray[$i]['renta_nat']));
    //         $separatedChargedRentaNat = explode('<br>', $this->strrtrim('<br>', $detailedArray[$i]['charged_renta_nat']));
    //         $separatedRentaNatType = explode('<br>', $this->strrtrim('<br>', $detailedArray[$i]['renta_nat_type']));
    //         $separatedPaidRentaNat = explode('<br>', $this->strrtrim('<br>', $detailedArray[$i]['paid_renta_nat']));
    //         $separatedUnpaidRentaNat = explode('<br>', $this->strrtrim('<br>', $detailedArray[$i]['unpaid_renta_nat']));
    //         $separatedUnpaidRentaNatValue = explode('<br>', $this->strrtrim('<br>', $detailedArray[$i]['unpaid_renta_nat_unit_value']));

    //         // calculate renta natura
    //         foreach ($separatedRentaNat as $key => $value) {
    //             if (('-' == $value || !$value) && ('-' == $separatedRentaNatType[$key] || !$separatedRentaNatType[$key])) {
    //                 $value = 'Без натура';
    //                 $separatedRentaNatType[$key] = '';
    //             }

    //             $detailedArray[$i]['renta_nat_text'] .= $value . ' ' . $separatedRentaNatType[$key] . ', ';

    //             $tmpRentaNatType = $separatedRentaNatType[$key];
    //             // Добавяне на всички уникални натури по договор в помощния масив
    //             // и добавяне на нови колони за всяка отделна рента
    //             // за всеки отделен резултат във формат
    //             // renta_nat_text_Натура(мерна единица)
    //             $detailedArray[$i]['renta_nat_text_' . $tmpRentaNatType] = number_format(floatval($value), 3, '.', '');
    //             if (!in_array($tmpRentaNatType, $this->uniqueNatura['renta_nat_text'])) {
    //                 array_push($this->uniqueNatura['renta_nat_text'], $tmpRentaNatType);
    //             }
    //         }

    //         // calculate charged renta natura
    //         foreach ($separatedChargedRentaNat as $key => $value) {
    //             if (('-' == $value || !$value) && ('-' == $separatedRentaNatType[$key] || !$separatedRentaNatType)) {
    //                 $value = 'Без натура';
    //                 $separatedRentaNatType[$key] = '';
    //             }

    //             $detailedArray[$i]['charged_renta_nat_text'] .= $value . ' ' . $separatedRentaNatType[$key] . '<br/>';

    //             $tmpChargedRentaNatType = $separatedRentaNatType[$key];
    //             // Добавяне на всички уникални начислени натури по договор в помощния масив
    //             // и добавяне на нови колони за всяка отделна натура
    //             // за всеки отделен резултат във формат
    //             // charged_renta_nat_text_Натура(мерна единица)
    //             $detailedArray[$i]['charged_renta_nat_text_' . $tmpChargedRentaNatType] = number_format(floatval($value), 3, '.', '');
    //             if (!in_array($tmpChargedRentaNatType, $this->uniqueNatura['charged_renta_nat_text'])) {
    //                 array_push($this->uniqueNatura['charged_renta_nat_text'], $tmpChargedRentaNatType);
    //             }
    //         }

    //         // calculate paid renta natura
    //         foreach ($separatedPaidRentaNat as $key => $value) {
    //             if (('-' == $value || !$value) && ('-' == $separatedPaidRentaNat[$key] || !$separatedPaidRentaNat)) {
    //                 $value = 'Без натура';
    //                 $separatedPaidRentaNat[$key] = '';
    //             }

    //             $tmpRentaNatTypeArray = explode('<br>', $separatedPaidRentaNat[$key]);
    //             foreach ($tmpRentaNatTypeArray as $tmpRenta) {
    //                 $currentPaidRenta = explode(' X ', $tmpRenta);

    //                 $tmpPaidRentaNatValue = $currentPaidRenta[0];
    //                 $tmpPaidRentaNatType = $currentPaidRenta[1];
    //                 // Добавяне на всички уникални изплатени натури в помощния масив
    //                 // и добавяне на нови колони за всяка отделна натура
    //                 // за всеки отделен резултат във формат
    //                 // paid_renta_nat_Натура(мерна единица)
    //                 if ($tmpPaidRentaNatType) {
    //                     $detailedArray[$i]['paid_renta_nat_' . $tmpPaidRentaNatType] = number_format(floatval($tmpPaidRentaNatValue), 3, '.', '');
    //                     if (!in_array($tmpPaidRentaNatType, $this->uniqueNatura['paid_renta_nat'])) {
    //                         array_push($this->uniqueNatura['paid_renta_nat'], $tmpPaidRentaNatType);
    //                     }
    //                 }
    //             }
    //         }

    //         // calculate unpaid renta natura
    //         foreach ($separatedUnpaidRentaNat as $key => $value) {
    //             if (('-' == $value || !$value) && ('-' == $separatedUnpaidRentaNat[$key] || !$separatedUnpaidRentaNat)) {
    //                 $value = 'Без натура';
    //                 $separatedUnpaidRentaNat[$key] = '';
    //             }

    //             $tmpRentaNatTypeArray = explode('<br>', $separatedUnpaidRentaNat[$key]);
    //             foreach ($tmpRentaNatTypeArray as $tmpRenta) {
    //                 $currentPaidRenta = explode(' X ', $tmpRenta);

    //                 $tmpPaidRentaNatValue = $currentPaidRenta[0];
    //                 $tmpPaidRentaNatType = $currentPaidRenta[1];
    //                 // Добавяне на всички уникални неизплатени натури
    //                 // и добавяне на нови колони за всяка отделна натура
    //                 // за всеки отделен резултат във формат
    //                 // unpaid_renta_Натура(мерна единица)
    //                 if ($tmpPaidRentaNatType) {
    //                     $detailedArray[$i]['unpaid_renta_nat_' . $tmpPaidRentaNatType] = number_format(floatval($tmpPaidRentaNatValue), 3, '.', '');
    //                     if (!in_array($tmpPaidRentaNatType, $this->uniqueNatura['unpaid_renta_nat'])) {
    //                         array_push($this->uniqueNatura['unpaid_renta_nat'], $tmpPaidRentaNatType);
    //                     }
    //                 }
    //             }
    //         }

    //         // calculate unpaid renta natura value
    //         foreach ($separatedUnpaidRentaNatValue as $key => $value) {
    //             if (('-' == $value || !$value) && ('-' == $separatedUnpaidRentaNatValue[$key] || !$separatedUnpaidRentaNatValue)) {
    //                 $value = 'Без натура';
    //                 $separatedUnpaidRentaNatValue[$key] = '';
    //             }

    //             $tmpRentaNatTypeArray = explode('<br>', $separatedUnpaidRentaNatValue[$key]);
    //             $counter = 0;
    //             // Добавяне на всички уникални неизплатени натури, умножени по единичната им стойност
    //             // и добавяне на нови колони за всяка отделна натура
    //             // за всеки отделен резултат във формат
    //             // unpaid_renta_nat_unit_value_Натура(мерна единица)
    //             foreach ($this->uniqueNatura['renta_nat_text'] as $tmpRenta) {
    //                 $detailedArray[$i]['unpaid_renta_nat_unit_value_' . $tmpRenta] = number_format(floatval($tmpRentaNatTypeArray[$counter]), 3, '.', '');
    //                 if (!in_array($tmpRenta, $this->uniqueNatura['unpaid_renta_nat_unit_value'])) {
    //                     array_push($this->uniqueNatura['unpaid_renta_nat_unit_value'], $tmpRenta);
    //                 }
    //                 $counter++;
    //             }
    //         }

    //         $detailedArray[$i]['renta_nat_text'] = $this->strrtrim('<br>', $detailedArray[$i]['renta_nat_text']);
    //         $detailedArray[$i]['charged_renta_nat_text'] = $this->strrtrim('<br>', $detailedArray[$i]['charged_renta_nat_text']);
    //     }

    //     usort($detailedArray, function ($elem1, $elem2) {
    //         return strcmp($elem1['owner_names'], $elem2['owner_names']);
    //     });

    //     return $detailedArray;
    // }

    // private function includeSeparatedRentas($headers)
    // {
    //     // Ако при обработката на резултатите има уникални
    //     // натури по договор се обработват
    //     if (!empty($this->uniqueNatura['renta_nat_text'])) {
    //         $renta_nat_text = [];
    //         // Създава се помощен масив, който държи всички уникални натури
    //         foreach ($this->uniqueNatura['renta_nat_text'] as $renta_nat) {
    //             $renta_nat_text['renta_nat_text_' . $renta_nat] = 'Рента по договор - ' . $renta_nat;
    //         }

    //         // Взима се местоположението на колоната Рента по договор
    //         $rentaNatTextIndex = $this->getAssocArrayIndex($headers, 'renta_nat_text', true);

    //         // Ако в експорта е включена колоната общата колона
    //         // на нейно място се поставят новите уникални
    //         // разделени натури
    //         if ($rentaNatTextIndex > -1) {
    //             $headers = array_slice($headers, 0, $rentaNatTextIndex, true) + $renta_nat_text + array_slice($headers, $rentaNatTextIndex, count($headers) - $rentaNatTextIndex, true);
    //             // сумарната колона се премахва
    //             unset($headers['renta_nat_text']);
    //         }
    //     }

    //     // Ако при обработката на резултатите има уникални
    //     // натури по договор се обработват
    //     if (!empty($this->uniqueNatura['charged_renta_nat_text'])) {
    //         $charged_renta_nat_text = [];

    //         // Създава се помощен масив, който държи всички уникални натури
    //         foreach ($this->uniqueNatura['charged_renta_nat_text'] as $renta_nat) {
    //             $charged_renta_nat_text['charged_renta_nat_text_' . $renta_nat] = 'Начислена рента в натура - ' . $renta_nat;
    //         }

    //         // Взима се местоположението на колоната Начислена рента в натура
    //         $rentaNatTextIndex = $this->getAssocArrayIndex($headers, 'charged_renta_nat_text', true);
    //         if ($rentaNatTextIndex > -1) {
    //             $headers = array_slice($headers, 0, $rentaNatTextIndex, true) + $charged_renta_nat_text + array_slice($headers, $rentaNatTextIndex, count($headers) - $rentaNatTextIndex, true);

    //             // сумарната колона се премахва
    //             unset($headers['charged_renta_nat_text']);
    //         }
    //     }

    //     // Ако при обработката на резултатите има уникални
    //     // натури по договор се обработват
    //     if (!empty($this->uniqueNatura['paid_renta_nat'])) {
    //         $paid_renta_nat = [];

    //         // Създава се помощен масив, който държи всички уникални натури
    //         foreach ($this->uniqueNatura['paid_renta_nat'] as $renta_nat) {
    //             $paid_renta_nat['paid_renta_nat_' . $renta_nat] = 'Платена рента в натура - ' . $renta_nat;
    //         }

    //         // Взима се местоположението на колоната Платена рента в натура
    //         $paidRentaNatTextIndex = $this->getAssocArrayIndex($headers, 'paid_renta_nat', true);
    //         if ($paidRentaNatTextIndex > -1) {
    //             $headers = array_slice($headers, 0, $paidRentaNatTextIndex, true) + $paid_renta_nat + array_slice($headers, $paidRentaNatTextIndex, count($headers) - $paidRentaNatTextIndex, true);

    //             // сумарната колона се премахва
    //             unset($headers['paid_renta_nat']);
    //         }
    //     }

    //     // Ако при обработката на резултатите има уникални
    //     // натури по договор се обработват
    //     if (!empty($this->uniqueNatura['unpaid_renta_nat'])) {
    //         $unpaid_renta_nat = [];

    //         // Създава се помощен масив, който държи всички уникални натури
    //         foreach ($this->uniqueNatura['unpaid_renta_nat'] as $renta_nat) {
    //             $unpaid_renta_nat['unpaid_renta_nat_' . $renta_nat] = 'Остатък в натура - ' . $renta_nat;
    //         }

    //         // Взима се местоположението на колоната Остатък в натура
    //         $unpaidRentaNatTextIndex = $this->getAssocArrayIndex($headers, 'unpaid_renta_nat', true);
    //         if ($unpaidRentaNatTextIndex > -1) {
    //             $headers = array_slice($headers, 0, $unpaidRentaNatTextIndex, true) + $unpaid_renta_nat + array_slice($headers, $unpaidRentaNatTextIndex, count($headers) - $unpaidRentaNatTextIndex, true);

    //             // сумарната колона се премахва
    //             unset($headers['unpaid_renta_nat']);
    //         }
    //     }

    //     // Ако при обработката на резултатите има уникални
    //     // натури по договор се обработват
    //     if (!empty($this->uniqueNatura['unpaid_renta_nat_unit_value'])) {
    //         $unpaid_renta_nat_unit_value = [];

    //         // Създава се помощен масив, който държи всички уникални натури
    //         foreach ($this->uniqueNatura['unpaid_renta_nat_unit_value'] as $renta_nat) {
    //             $unpaid_renta_nat_unit_value['unpaid_renta_nat_unit_value_' . $renta_nat] = 'Остатък в натура по ед. ст. - ' . $renta_nat;
    //         }

    //         // Взима се местоположението на колоната Остатък в натура по ед. ст.
    //         $unpaidRentaNatUnitValue = $this->getAssocArrayIndex($headers, 'unpaid_renta_nat_unit_value', true);
    //         if ($unpaidRentaNatUnitValue > -1) {
    //             $headers = array_slice($headers, 0, $unpaidRentaNatUnitValue, true) + $unpaid_renta_nat_unit_value + array_slice($headers, $unpaidRentaNatUnitValue, count($headers) - $unpaidRentaNatUnitValue, true);

    //             // сумарната колона се премахва
    //             unset($headers['unpaid_renta_nat_unit_value']);
    //         }
    //     }

    //     // Ако при обработката на резултатите има уникални
    //     // натури по договор се обработват
    //     if (!empty($this->uniqueNatura['over_paid_renta_nat'])) {
    //         $over_paid_renta_nat = [];

    //         // Създава се помощен масив, който държи всички уникални натури
    //         foreach ($this->uniqueNatura['over_paid_renta_nat'] as $renta_nat) {
    //             $over_paid_renta_nat['over_paid_renta_nat_' . $renta_nat] = 'Надплатена рента в натура - ' . $renta_nat;
    //         }

    //         // Взима се местоположението на колоната Надплатена рента в натура
    //         $overpaidRentaNat = $this->getAssocArrayIndex($headers, 'over_paid_renta_nat', true);
    //         if ($overpaidRentaNat > -1) {
    //             $headers = array_slice($headers, 0, $overpaidRentaNat, true) + $over_paid_renta_nat + array_slice($headers, $overpaidRentaNat, count($headers) - $overpaidRentaNat, true);

    //             // сумарната колона се премахва
    //             unset($headers['over_paid_renta_nat']);
    //         }
    //     }

    //     // Ако при обработката на резултатите има уникални
    //     // натури по договор се обработват
    //     if (!empty($this->uniqueNatura['paid_renta_by'])) {
    //         $paid_renta_by = [];

    //         // Създава се помощен масив, който държи всички уникални натури
    //         foreach ($this->uniqueNatura['paid_renta_by'] as $renta_nat) {
    //             $paid_renta_by['paid_renta_by_' . $renta_nat] = 'Платена рента чрез - ' . $renta_nat;
    //         }

    //         // Взима се местоположението на колоната Платена рента чрез
    //         $paidRentaByIndex = $this->getAssocArrayIndex($headers, 'paid_renta_by', true);
    //         if ($paidRentaByIndex > -1) {
    //             $headers = array_slice($headers, 0, $paidRentaByIndex, true) + $paid_renta_by + array_slice($headers, $paidRentaByIndex, count($headers) - $paidRentaByIndex, true);

    //             // сумарната колона се премахва
    //             unset($headers['paid_renta_by']);
    //         }
    //     }

    //     return $headers;
    // }

    // private function transformSeparateRentaColumnsToCellArrays($columns)
    // {
    //     // Обикалят се всички уникални натури
    //     // и се трансформират в масив, който съдържа информация
    //     // за колоните, които да бъдат включени в експорта
    //     if (!empty($this->uniqueNatura['renta_nat_text'])) {
    //         $rentaNatCells = [];

    //         foreach ($this->uniqueNatura['renta_nat_text'] as $renta_nat) {
    //             $rentaNatCells['renta_nat_text_' . $renta_nat] = [
    //                 'text' => 'Рента по договор - ' . $renta_nat,
    //                 'width' => 150,
    //                 'summable' => true,
    //             ];
    //         }
    //         $rentaNatTextIndex = $this->getAssocArrayIndex($columns, 'renta_nat_text', true);

    //         if ($rentaNatTextIndex > -1) {
    //             $columns = array_slice($columns, 0, $rentaNatTextIndex, true) + $rentaNatCells + array_slice($columns, $rentaNatTextIndex, count($columns) - $rentaNatTextIndex, true);
    //             unset($columns['renta_nat_text']);
    //         }
    //     }
    //     // Обикалят се всички уникални натури
    //     // и се трансформират в масив, който съдържа информация
    //     // за колоните, които да бъдат включени в експорта
    //     if (!empty($this->uniqueNatura['charged_renta_nat_text'])) {
    //         $chargedRentaNat = [];

    //         foreach ($this->uniqueNatura['charged_renta_nat_text'] as $renta_nat) {
    //             $chargedRentaNat['charged_renta_nat_text_' . $renta_nat] = [
    //                 'text' => 'Начислена рента в натура - ' . $renta_nat,
    //                 'width' => 150,
    //                 'summable' => true,
    //             ];
    //         }
    //         $rentaNatTextIndex = $this->getAssocArrayIndex($columns, 'charged_renta_nat_text', true);

    //         if ($rentaNatTextIndex > -1) {
    //             $columns = array_slice($columns, 0, $rentaNatTextIndex, true) + $chargedRentaNat + array_slice($columns, $rentaNatTextIndex, count($columns) - $rentaNatTextIndex, true);
    //             unset($columns['charged_renta_nat_text']);
    //         }
    //     }

    //     // Обикалят се всички уникални натури
    //     // и се трансформират в масив, който съдържа информация
    //     // за колоните, които да бъдат включени в експорта
    //     if (!empty($this->uniqueNatura['paid_renta_nat'])) {
    //         $paidRentaNat = [];

    //         foreach ($this->uniqueNatura['paid_renta_nat'] as $renta_nat) {
    //             $paidRentaNat['paid_renta_nat_' . $renta_nat] = [
    //                 'text' => 'Платена рента в натура - ' . $renta_nat,
    //                 'width' => 150,
    //                 'summable' => true,
    //             ];
    //         }
    //         $paidRentaNatTextIndex = $this->getAssocArrayIndex($columns, 'paid_renta_nat', true);

    //         if ($paidRentaNatTextIndex > -1) {
    //             $columns = array_slice($columns, 0, $paidRentaNatTextIndex, true) + $paidRentaNat + array_slice($columns, $paidRentaNatTextIndex, count($columns) - $paidRentaNatTextIndex, true);
    //             unset($columns['paid_renta_nat']);
    //         }
    //     }
    //     // Обикалят се всички уникални натури
    //     // и се трансформират в масив, който съдържа информация
    //     // за колоните, които да бъдат включени в експорта
    //     if (!empty($this->uniqueNatura['unpaid_renta_nat'])) {
    //         $paidRentaNat = [];

    //         foreach ($this->uniqueNatura['unpaid_renta_nat'] as $renta_nat) {
    //             $paidRentaNat['unpaid_renta_nat_' . $renta_nat] = [
    //                 'text' => 'Остатък в натура - ' . $renta_nat,
    //                 'width' => 150,
    //                 'summable' => true,
    //             ];
    //         }
    //         $unpaidRentaNatTextIndex = $this->getAssocArrayIndex($columns, 'unpaid_renta_nat', true);

    //         if ($unpaidRentaNatTextIndex > -1) {
    //             $columns = array_slice($columns, 0, $unpaidRentaNatTextIndex, true) + $paidRentaNat + array_slice($columns, $unpaidRentaNatTextIndex, count($columns) - $unpaidRentaNatTextIndex, true);
    //             unset($columns['unpaid_renta_nat']);
    //         }
    //     }
    //     // Обикалят се всички уникални натури
    //     // и се трансформират в масив, който съдържа информация
    //     // за колоните, които да бъдат включени в експорта
    //     if (!empty($this->uniqueNatura['unpaid_renta_nat_unit_value'])) {
    //         $unpaidRentaNatUnitValue = [];

    //         foreach ($this->uniqueNatura['unpaid_renta_nat_unit_value'] as $renta_nat) {
    //             $unpaidRentaNatUnitValue['unpaid_renta_nat_unit_value_' . $renta_nat] = [
    //                 'text' => 'Остатък в натура по ед. ст. - ' . $renta_nat,
    //                 'width' => 150,
    //                 'summable' => true,
    //             ];
    //         }
    //         $unpaidRentaNatUnitValueTextIndex = $this->getAssocArrayIndex($columns, 'unpaid_renta_nat_unit_value', true);

    //         if ($unpaidRentaNatUnitValueTextIndex > -1) {
    //             $columns = array_slice($columns, 0, $unpaidRentaNatUnitValueTextIndex, true) + $unpaidRentaNatUnitValue + array_slice($columns, $unpaidRentaNatUnitValueTextIndex, count($columns) - $unpaidRentaNatUnitValueTextIndex, true);
    //             unset($columns['unpaid_renta_nat_unit_value']);
    //         }
    //     }
    //     // Обикалят се всички уникални натури
    //     // и се трансформират в масив, който съдържа информация
    //     // за колоните, които да бъдат включени в експорта
    //     if (!empty($this->uniqueNatura['over_paid_renta_nat'])) {
    //         $overpaidRentaNatUnitValue = [];

    //         foreach ($this->uniqueNatura['over_paid_renta_nat'] as $renta_nat) {
    //             $overpaidRentaNatUnitValue['over_paid_renta_nat_' . $renta_nat] = [
    //                 'text' => 'Надплатена рента в натура - ' . $renta_nat,
    //                 'width' => 150,
    //                 'summable' => true,
    //             ];
    //         }
    //         $overpaidRentaNatUnitValueTextIndex = $this->getAssocArrayIndex($columns, 'over_paid_renta_nat', true);

    //         if ($overpaidRentaNatUnitValueTextIndex > -1) {
    //             $columns = array_slice($columns, 0, $overpaidRentaNatUnitValueTextIndex, true) + $overpaidRentaNatUnitValue + array_slice($columns, $overpaidRentaNatUnitValueTextIndex, count($columns) - $overpaidRentaNatUnitValueTextIndex, true);
    //             unset($columns['over_paid_renta_nat']);
    //         }
    //     }
    //     // Обикалят се всички уникални натури
    //     // и се трансформират в масив, който съдържа информация
    //     // за колоните, които да бъдат включени в експорта
    //     if (!empty($this->uniqueNatura['paid_renta_by'])) {
    //         $paidRentaBy = [];

    //         foreach ($this->uniqueNatura['paid_renta_by'] as $renta_nat) {
    //             $paidRentaBy['paid_renta_by_' . $renta_nat] = [
    //                 'text' => 'Платена рента чрез - ' . $renta_nat,
    //                 'width' => 150,
    //                 'summable' => true,
    //             ];
    //         }
    //         $paidRentaByTextIndex = $this->getAssocArrayIndex($columns, 'paid_renta_by', true);

    //         if ($paidRentaByTextIndex > -1) {
    //             $columns = array_slice($columns, 0, $paidRentaByTextIndex, true) + $paidRentaBy + array_slice($columns, $paidRentaByTextIndex, count($columns) - $paidRentaByTextIndex, true);
    //             unset($columns['paid_renta_by']);
    //         }
    //     }

    //     return $columns;
    // }

    // Помощен метод, който да сумира необходимите колони при принтиране на ведомост
    // private function calculateFooterSummaryForPrinting($rowData, $separated_natura)
    // {
    //     if (!$separated_natura) {
    //         return $this->footerArray;
    //     }

    //     $tmpUniqueNaturas = [];
    //     $total_area = 0;
    //     $total_pu_area = 0;
    //     $total_renta = 0;
    //     $total_charged_renta = 0;
    //     $total_paid_renta = 0;
    //     $total_unpaid_renta = 0;
    //     $total_total_by_renta = 0;
    //     $total_over_paid = 0;

    //     foreach ($this->uniqueNatura['renta_nat_text'] as $key => $value) {
    //         if (!array_key_exists('renta_nat_text_' . $value, $tmpUniqueNaturas)) {
    //             $tmpUniqueNaturas['renta_nat_text_' . $value] = 0;
    //         }
    //     }
    //     foreach ($this->uniqueNatura['charged_renta_nat_text'] as $key => $value) {
    //         if (!array_key_exists('charged_renta_nat_text_' . $value, $tmpUniqueNaturas)) {
    //             $tmpUniqueNaturas['charged_renta_nat_text_' . $value] = 0;
    //         }
    //     }
    //     foreach ($this->uniqueNatura['paid_renta_by'] as $key => $value) {
    //         if (!array_key_exists('paid_renta_by_' . $value, $tmpUniqueNaturas)) {
    //             $tmpUniqueNaturas['paid_renta_by_' . $value] = 0;
    //         }
    //     }
    //     foreach ($this->uniqueNatura['paid_renta_nat'] as $key => $value) {
    //         if (!array_key_exists('paid_renta_nat_' . $value, $tmpUniqueNaturas)) {
    //             $tmpUniqueNaturas['paid_renta_nat_' . $value] = 0;
    //         }
    //     }
    //     foreach ($this->uniqueNatura['paid_renta_nat_by'] as $key => $value) {
    //         if (!array_key_exists('paid_renta_nat_by_' . $value, $tmpUniqueNaturas)) {
    //             $tmpUniqueNaturas['paid_renta_nat_by_' . $value] = 0;
    //         }
    //     }
    //     foreach ($this->uniqueNatura['paid_renta_nat_by_detailed'] as $key => $value) {
    //         if (!array_key_exists('paid_renta_nat_by_detailed_' . $value, $tmpUniqueNaturas)) {
    //             $tmpUniqueNaturas['paid_renta_nat_by_detailed_' . $value] = 0;
    //         }
    //     }
    //     foreach ($this->uniqueNatura['unpaid_renta_nat'] as $key => $value) {
    //         if (!array_key_exists('unpaid_renta_nat_' . $value, $tmpUniqueNaturas)) {
    //             $tmpUniqueNaturas['unpaid_renta_nat_' . $value] = 0;
    //         }
    //     }
    //     foreach ($this->uniqueNatura['unpaid_renta_nat_unit_value'] as $key => $value) {
    //         if (!array_key_exists('unpaid_renta_nat_unit_value_' . $value, $tmpUniqueNaturas)) {
    //             $tmpUniqueNaturas['unpaid_renta_nat_unit_value_' . $value] = 0;
    //         }
    //     }
    //     foreach ($this->uniqueNatura['over_paid_renta_nat'] as $key => $value) {
    //         if (!array_key_exists('over_paid_renta_nat_' . $value, $tmpUniqueNaturas)) {
    //             $tmpUniqueNaturas['over_paid_renta_nat_' . $value] = 0;
    //         }
    //     }
    //     foreach ($this->uniqueNatura['total_by_renta_nat'] as $key => $value) {
    //         if (!array_key_exists('total_by_renta_nat_' . $value, $tmpUniqueNaturas)) {
    //             $tmpUniqueNaturas['total_by_renta_nat_' . $value] = 0;
    //         }
    //     }

    //     foreach ($rowData['rows'] as $row) {
    //         $total_renta += floatval($row['renta']);
    //         $total_area += floatval($row['area']);
    //         $total_pu_area += floatval($row['pu_area']);
    //         $total_charged_renta += floatval($row['charged_renta']);
    //         $total_paid_renta += floatval($row['paid_renta']);
    //         $total_unpaid_renta += floatval($row['unpaid_renta']);
    //         $total_total_by_renta += floatval($row['total_by_renta']);
    //         $total_over_paid += floatval($row['over_paid']);

    //         foreach ($row as $key => $value) {
    //             if (array_key_exists($key, $tmpUniqueNaturas)) {
    //                 $tmpUniqueNaturas[$key] += is_numeric($value) ? $value : 0;
    //             }
    //         }
    //     }

    //     $tmpUniqueNaturas['area'] = number_format($total_area, 3, ',', '');
    //     $tmpUniqueNaturas['egn_eik'] = 'ОБЩО';
    //     $tmpUniqueNaturas['renta'] = number_format($total_renta, 2, ',', '');
    //     $tmpUniqueNaturas['charged_renta'] = number_format($total_charged_renta, 2, ',', '');
    //     $tmpUniqueNaturas['paid_renta'] = number_format($total_paid_renta, 2, ',', '');
    //     $tmpUniqueNaturas['unpaid_renta'] = number_format($total_unpaid_renta, 2, ',', '');
    //     $tmpUniqueNaturas['total_by_renta'] = number_format($total_total_by_renta, 2, ',', '');
    //     $tmpUniqueNaturas['over_paid'] = number_format($total_over_paid, 2, ',', '');
    //     $tmpUniqueNaturas['pu_area'] = number_format($total_pu_area, 3, ',', '');

    //     return $tmpUniqueNaturas;
    // }

    /**
     * Formats the return data for payroll grid export.
     *
     * @param array $data the data to be formatted
     * @param array $columns the columns to include in the formatted output
     *
     * @return array the formatted data ready for export
     */
    private function formatReturnData($data, $columns)
    {
        foreach ($data as $key => $row) {
            $returnData[$key]['ownerNames'] = $row['owner_names'];
            $returnData[$key]['areaForRent'] = $row['is_dead'] ? '' : $row['all_owner_area'];
            $returnData[$key]['rentMoney'] = $row['is_dead'] ? '' : $row['all_renta_money'];
            $returnData[$key]['rentMoneyEuro'] = $row['is_dead'] ? '' : convertBGNtoEURO($row['all_renta_money']);
            $returnData[$key]['paidRentMoney'] = $row['paid_renta'];
            $returnData[$key]['paidRentMoneyEuro'] = $row['is_dead'] ? '' : convertBGNtoEURO($row['paid_renta']);
            $returnData[$key]['unpaidRentMoney'] = $row['is_dead'] ? '' : $row['unpaid_renta'];
            $returnData[$key]['unpaidRentMoneyEuro'] = $row['is_dead'] ? '' : convertBGNtoEURO($row['unpaid_renta']);
            $returnData[$key]['overpaidRentMoney'] = $row['is_dead'] ? '' : $row['overpaid_renta'];
            $returnData[$key]['overpaidRentMoneyEuro'] = $row['is_dead'] ? '' : convertBGNtoEURO($row['overpaid_renta']);

            $returnData[$key]['rentInKind'] = $row['is_dead'] ? '' : str_replace('<br>', "\n", rtrim($row['all_renta_nat_with_type'], '<br>'));
            $returnData[$key]['paidRentInKind'] = str_replace('<br>', "\n", rtrim($row['paid_renta_nat_with_type'], '<br>'));
            $returnData[$key]['unpaidRentInKind'] = $row['is_dead'] ? '' : str_replace('<br>', "\n", rtrim($row['unpaid_renta_nat_with_type'], '<br>'));
            $returnData[$key]['overpaidRentInKind'] = $row['is_dead'] ? '' : str_replace('<br>', "\n", rtrim($row['overpaid_renta_nat_with_type'], '<br>'));

            if ($columns['inheritorOf']) {
                $returnData[$key]['inheritorOf'] = $row['owner_parent_names'];
            }
            if ($columns['ownerEgnEik']) {
                $returnData[$key]['ownerEgnEik'] = $row['egn_eik'];
            }

            if ($columns['ownerPhone']) {
                $returnData[$key]['ownerPhone'] = $row['mobile'] . (!empty($row['phone']) ? ', ' . $row['phone'] : '');
            }

            if ($columns['ownerIban']) {
                $returnData[$key]['ownerIban'] = $row['iban'];
            }

            if ($columns['repNames']) {
                $returnData[$key]['repNames'] = implode("\n", $row['rep_names_array']);
            }

            if ($columns['repIban']) {
                $returnData[$key]['repIban'] = implode("\n", $row['rep_ibans_array']);
            }

            if ($columns['contracts']) {
                $returnData[$key]['contracts'] = empty($row['c_num_group_name_array']) ? $row['c_num_with_group_name'] : implode("\n", $row['c_num_group_name_array']);
            }

            if ($columns['rentPlace']) {
                $returnData[$key]['rentPlace'] = $row['rent_place_name'];
            }

            if ($columns['plots']) {
                $returnData[$key]['plots'] = implode("\n", $row['kad_idents_array']);
            }

            if ($columns['plotDetailed']) {
                $returnData[$key]['plotDetailed'] = $row['kad_ident'];
            }

            if ($columns['contractArea']) {
                $returnData[$key]['contractArea'] = $row['is_dead'] ? '' : $row['all_owner_no_rounded_contract'];
            }

            if ($columns['arableArea']) {
                $returnData[$key]['arableArea'] = $row['is_dead'] ? '' : $row['cultivated_area'];
            }

            if ($columns['personalUseArea']) {
                $returnData[$key]['personalUseArea'] = $row['is_dead'] ? '' : str_replace('-', 0, $row['pu_area']);
            }

            if ($columns['natsDetailed']) {
                foreach ($this->usedRentaTypes as $rentaNatId) {
                    $rentNat = isset($row['renta_nat'][$rentaNatId]) && is_numeric($row['renta_nat'][$rentaNatId]) ? $row['renta_nat'][$rentaNatId] : 0;
                    $chargedRentNat = isset($row['charged_renta_nat'][$rentaNatId]) && is_numeric($row['charged_renta_nat'][$rentaNatId]) ? $row['charged_renta_nat'][$rentaNatId] : 0;
                    $natSum = $rentNat + $chargedRentNat;
                    $returnData[$key][self::RENT_NAT_PREFIX . $rentaNatId] = $row['is_dead'] ? '' : $natSum;

                    $returnData[$key][self::PAID_RENT_NAT_PREFIX . $rentaNatId] = isset($row['paid_renta_nat_sum'][$rentaNatId]) && is_numeric($row['paid_renta_nat_sum'][$rentaNatId]) ? $row['paid_renta_nat_sum'][$rentaNatId] : 0;
                    $returnData[$key][self::UNPAID_RENT_NAT_PREFIX . $rentaNatId] = $row['is_dead'] ? '' : isset($row['unpaid_renta_nat_arr'][$rentaNatId]) && is_numeric($row['unpaid_renta_nat_arr'][$rentaNatId]) ? $row['unpaid_renta_nat_arr'][$rentaNatId] : 0;
                    $returnData[$key][self::OVERPAID_RENT_NAT_PREFIX . $rentaNatId] = $row['is_dead'] ? '' : isset($row['overpaid_renta_nat_arr'][$rentaNatId]) && is_numeric($row['overpaid_renta_nat_arr'][$rentaNatId]) ? $row['overpaid_renta_nat_arr'][$rentaNatId] : 0;

                    if (!isset($natsValues[$rentaNatId])) {
                        $natsValues[$rentaNatId] = 0;
                    }

                    // Sum the rent in kind values for each type
                    $natsValues[$rentaNatId] += $natSum;
                }
            }
        }

        return [
            'natsValues' => $natsValues,
            'data' => $returnData,
        ];
    }

    /**
     * Generates header rows for the payroll grid export.
     *
     * @param array $columns an array of column definitions to be included in the header
     * @param array $data the data rows that may be used to determine dynamic (rent in kind) headers or formatting
     */
    private function generateHeaders($columns, $data)
    {
        foreach ($columns as $column => $value) {
            if (!$value) {
                unset($this->headers[$column]);
            }
        }

        if ($this->headers['natsDetailed']) {
            unset($this->headers['natsDetailed'], $this->headers['rentInKind'], $this->headers['paidRentInKind'], $this->headers['unpaidRentInKind'], $this->headers['overpaidRentInKind']);
            $this->unsetArrayElementByValue($this->mergeRowsHeaders, 'paidRentInKind');
            $this->unsetArrayElementByValue($this->mergeRowsHeaders, 'unpaidRentInKind');
            $this->unsetArrayElementByValue($this->mergeRowsHeaders, 'overpaidRentInKind');

            // Generate headers for rent in kind. It is needed to be separated for each type of rent in kind, in order to be ordered by group.
            // For example first headers will be for rents in kind, after all rents in kind are filled, the next headers will be for paid rents in kind, then unpaid rents in kind and so on.
            foreach ($data as $row) {
                foreach ($row['renta_nat_info'] as $rentaNat) {
                    $this->fillHeadersInfo($rentaNat, self::RENT_NAT_PREFIX, 'Рента в натура', $columns);
                }
            }
            foreach ($data as $row) {
                foreach ($row['renta_nat_info'] as $rentaNat) {
                    $this->fillHeadersInfo($rentaNat, self::PAID_RENT_NAT_PREFIX, 'Платена рента в натура', $columns);
                }
            }
            foreach ($data as $row) {
                foreach ($row['renta_nat_info'] as $rentaNat) {
                    $this->fillHeadersInfo($rentaNat, self::UNPAID_RENT_NAT_PREFIX, 'Оставаща рента в натура', $columns);
                }
            }
            foreach ($data as $row) {
                foreach ($row['renta_nat_info'] as $rentaNat) {
                    $this->fillHeadersInfo($rentaNat, self::OVERPAID_RENT_NAT_PREFIX, 'Надплатена рента в натура', $columns);
                }
            }
        }

        $this->headers['date'] = 'Дата';
        $this->headers['receiver'] = 'Получил';
        $this->headers['sign'] = 'Подпис';
    }

    private function fillHeadersInfo($rentaNat, $prefix, $label, $columns)
    {
        $natKey = $prefix . $rentaNat['renta_nat_id'];

        $this->headers[$natKey] = $label . ': ' . $rentaNat['renta_nat_name'] . ' (' . $rentaNat['unit_name'] . ')';

        if (!in_array($this->headers[$natKey], $this->summableColumns)) {
            $this->summableColumns[] = $this->headers[$natKey];
        }

        if (!in_array($natKey, $this->mergeRowsHeaders) && (false == $columns['natsDetailed'] || self::RENT_NAT_PREFIX != $prefix)) {
            $this->mergeRowsHeaders[] = $natKey;
        }

        if (!in_array($rentaNat['renta_nat_id'], $this->usedRentaTypes)) {
            $this->usedRentaTypes[] = $rentaNat['renta_nat_id'];
        }
    }

    /**
     * Separates owners by their associated plots for a given year.
     *
     * @param int $year the year for which the separation is to be performed
     * @param array $owners an array of owner data to be separated by plots
     *
     * @return array returns an array where owners are grouped by their plots
     */
    private function separateOwnersByPlots($year, array $owners): array
    {
        $result = [];
        $paymentsController = new PaymentsController($this->User->Database);

        foreach ($owners as $owner) {
            $ownerResult = $paymentsController->getOwnerPayroll($year, $owner['owner_id'], $owner['path']);

            $result = array_merge($result, $ownerResult);
        }

        return $result;
    }

    /**
     * Retrieves the leaf owners from a hierarchical array of owners.
     *
     * This method processes the provided array of owners and returns an array
     * containing only the leaf nodes (owners without children) in the hierarchy.
     *
     * @param array $owners the hierarchical array of owners to process
     *
     * @return array an array of leaf owners extracted from the input
     */
    private function getLeafOwners(array $owners): array
    {
        $result = [];
        foreach ($owners as $owner) {
            // If the owner has no children meants that the owner is live and add it to the result
            // If the owner has children but has paid rent as well, add it to the result but show only the paid rents, all other colums like areas or rents have to be set to 0
            // This is needed for correctly displaying the payroll totals. In other case, the payroll totals will be with duplicated areas for examples (for the parent and children).
            if (empty($owner['children']) || (!empty($owner['children']) && $owner['paid_renta'] > 0)) {
                $result[] = $owner;
            }

            if (!empty($owner['children'])) {
                $result = array_merge($result, $this->getLeafOwners($owner['children']));
            }
        }

        return $result;
    }

    /**
     * Aggregates owner information into a structured array.
     *
     * @param array $owners array of owner data to be aggregated
     * @param bool $includeParentId Optional. Whether to include the parent ID in the aggregation. Default is false.
     * @param bool $inpludePlotId Optional. Whether to include the plot ID in the aggregation. Default is false.
     *
     * @return array the aggregated owner information
     */
    private function aggregateOwners(array $owners, bool $includeParentId = false, $inpludePlotId = false): array
    {
        $paymentsController = new PaymentsController($this->User->Database);

        $result = [];
        foreach ($owners as $owner) {
            $ownerKey = $owner['owner_id'];

            if ($includeParentId) {
                $ownerKey .= '_' . $owner['owner_parent_id'];
            }
            if ($inpludePlotId) {
                $ownerKey .= '_' . $owner['plot_id'];
            }

            if (!isset($result[$ownerKey])) {
                $result[$ownerKey] = $owner;
            } else {
                $result[$ownerKey] = $paymentsController->calculateOwnerData($result[$ownerKey], $owner);
            }
        }

        return $result;
    }

    // Помощен метод, който да вземе индекса на даден ключ/стойност
    // в асоциативен масив
    private function getAssocArrayIndex($array, $searchedKey, $searchByKey = false)
    {
        $tmpCounter = 0;
        if ($searchByKey) {
            if (!array_key_exists($searchedKey, $array)) {
                return -1;
            }
            foreach ($array as $key => $value) {
                if ($key != $searchedKey) {
                    $tmpCounter++;
                } else {
                    return $tmpCounter;
                }
            }
        } else {
            if (!array_search($searchedKey, $array)) {
                return -1;
            }
            foreach ($array as $key => $value) {
                if ($key != $searchedKey) {
                    $tmpCounter++;
                } else {
                    return $tmpCounter;
                }
            }
        }
    }

    // Помощен метод, който да създава footer-a
    // и добавя формула за сума
    // на база брой резултати и
    // информация дали колоната трябва да бъде сумирана или не
    private function createFooter($data)
    {
        $countRows = count($data);

        foreach ($this->headers as $key => $value) {
            $footer[$key] = '';
        }

        $footer['ownerNames'] = 'ОБЩО';
        $start = 2;
        $end = $countRows + 1;

        $headerKeys = array_keys($this->headers);
        $headerValues = array_values($this->headers);

        $letters = $this->mapColumnIndex($this->headers);

        foreach ($this->summableColumns as $column) {
            $index = array_search($column, $headerValues);
            if ($index) {
                $tmpColumn = $headerKeys[$index];
                $footer[$tmpColumn] = '=ROUND(SUM(' . $letters[$index] . $start . ':' . $letters[$index] . $end . '), 3)';
            }
        }

        return [$footer];
    }

    // Помощен метод, който взема всички колони,
    // отбелязани като колони за сумиране,
    // добавяйки ги в отделен помощен масив
    private function getSummableColumns($columns)
    {
        foreach ($columns as $column) {
            if ($column['summable']) {
                array_push($this->summableColumns, $column['text']);
            }
        }
    }

    // Помощен метод, който взема
    // индексите на отделните колони, в
    // зависимост от броя на всички колони,
    // които ще участват в експорта.
    private function mapColumnIndex($headers)
    {
        $columnIndexes = [];
        $letters = range('A', 'Z');
        $headersCount = count($headers);
        $letterCount = count($letters);
        $finalLetters = $letters;
        for ($i = 0; $i < $letterCount; $i++) {
            $finalLetters[] = 'A' . $letters[$i];
        }
        for ($i = 0; $i < $letterCount; $i++) {
            $finalLetters[] = 'B' . $letters[$i];
        }
        for ($i = 0; $i < $letterCount; $i++) {
            $finalLetters[] = 'C' . $letters[$i];
        }

        return $finalLetters;
    }

    /**
     * returns the children of a person.
     *
     * @param array $parent
     * @param string $parentNumber
     */
    private function _getChildren($parent, $parentNumber)
    {
        if (is_array($parent['children'])) {
            for ($j = 0; $j < $children_count = count($parent['children']); $j++) {
                $heritor = $parent['children'][$j];
                $heritor['farming_year'] = $parent['farming_year'];

                $heritor['number'] = $parentNumber . '.' . ($j + 1);
                $this->allOwnersForPayroll[] = $heritor;
                if (!empty($heritor['children'])) {
                    $this->_getChildren($heritor, $parentNumber . '.' . ($j + 1));
                }
            }
        }
    }

    private function strrtrim($strip, $message)
    {
        // break message apart by strip string
        $lines = explode($strip, $message);
        $last = '';
        // pop off empty strings at the end
        do {
            $last = array_pop($lines);
        } while (empty($last) && (count($lines)));
        // re-assemble what remains
        return implode($strip, array_merge($lines, [$last]));
    }

    private function setEkateData()
    {
        $UsersController = new UsersController('Users');
        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'return' => ['DISTINCT(ekate)'],
            'tablename' => $UserDbController->DbHandler->tableKVS,
        ];

        $results = $UserDbController->getItemsByParams($options);

        $return = [];
        $countResults = count($results);
        // todo: make a single query using in , like select ekatte_code , ekatte_name from su_ekatte WHERE ekatte_code IN ('73242' , '46749')
        for ($i = 0; $i < $countResults; $i++) {
            $return[$results[$i]['ekate']] = $UsersController->getEkatteName($results[$i]['ekate']) . ' (' . $results[$i]['ekate'] . ')';
        }
        $this->ekateData = $return;
    }

    private function removeUnusedNats($natValues)
    {
        foreach ($natValues as $key => $value) {
            if (0 == $value) {
                unset($this->headers[self::RENT_NAT_PREFIX . $key], $this->headers[self::PAID_RENT_NAT_PREFIX . $key], $this->headers[self::UNPAID_RENT_NAT_PREFIX . $key], $this->headers[self::OVERPAID_RENT_NAT_PREFIX . $key]);
            }
        }
    }

    private function getColumnLetterByHeader($header)
    {
        $letters = range('A', 'Z');

        // get the position of key $header in $this->headers
        $keys = array_keys($this->headers);
        $index = array_search($header, $keys);

        if (false === $index) {
            return '';
        }

        // Calculate the column letter based on the index
        $columnLetter = '';
        while ($index >= 0) {
            $columnLetter = $letters[$index % 26] . $columnLetter;
            $index = (int)($index / 26) - 1;
        }

        return $columnLetter;
    }

    private function unsetArrayElementByValue(&$array, $value)
    {
        $key = array_search($value, $array);
        if (false !== $key) {
            unset($array[$key]);
        }
    }
}
