<?php

namespace TF\Engine\APIClasses\Payments;

use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Common\Config;
use TF\Engine\Kernel\ExportToExcelClass;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbPayments\UserDbPaymentsController;

// Prado::using('Plugins.Core.UserDb.*');
// Prado::using('Plugins.Core.UserDb.conf');
// Prado::using('Plugins.Core.Contracts.conf');
// Prado::using('Plugins.Core.Farming.conf');

/**
 * Справка Изплатено в брой/по банков път и в натура.
 *
 * @rpc-module Payments
 *
 * @rpc-service-id payments-bank-and-natura-payment-report
 *
 * @property UserDbPaymentsController $UserDbPaymentsController
 * @property UserDbController $UserDbController
 */
class PaymentsBankAndNaturaPaymentReport extends TRpcApiProvider
{
    // Initialize helper variables
    private $renta_types = [];
    private $renta_types_units = [];
    private $renta_types_values = [];
    private $total = [];
    private $totalAll = [];

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getPaymentsBankAndNaturaPaymentReport'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'export' => ['method' => [$this, 'exportPaymentsBankAndNaturaPaymentReport']],
        ];
    }

    /**
     * Метод за попълване на грида.
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer pbb_year
     *                         #item date pbb_date_from
     *                         #item date pbb_date_to
     *                         #item boolean pbb_payment_type
     *                         #item integer pbb_renta_type
     *                         #item string pbb_paid_to
     *                         #item string pbb_paid_by
     *                         #item string pbb_bank_acc
     *                         }
     * @param int $page pagination parameter
     * @param int $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array
     */
    public function getPaymentsBankAndNaturaPaymentReport(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        // Initialize the method controllers
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);
        $FarmingController = new FarmingController('Farming');

        $dateFrom = $rpcParams['pbbn_date_from'];
        $dateTo = $rpcParams['pbbn_date_to'];

        // Тази дата се използва за начална дата на справката.
        // Не трябва да се визуализират данни за плащания
        // Правени преди тази дата.
        $beginDate = '2016-01-01';
        // Прави се проверка дали има зададена начална дата на справката
        // Ако има начална дата, то се прави проверка дали е преди 01 Януари 2016
        // и ако не е се слага за начална дата 01 Януари 2016
        // След това се прави проверка дали крайната дата е по-голяма от началната
        // и в случай, че не е се добавят 7 дни и по този начин справката е за период от 7 дни след началната дата
        if ($dateFrom) {
            $dateFrom = $dateFrom >= $beginDate ? $dateFrom : $beginDate;

            if ($dateTo) {
                $dateTo = $dateTo >= $dateFrom ? $dateTo : date('Y-m-d', strtotime($dateFrom . ' + 7 days'));
            }

            // Ако няма зададена начална дата
            // изрично се задава начална дата на справката
            // $beginDate, която е зададена да бъде 01 Януари 2016г.
            // За да не се връщат резултати преди тази дата
        } else {
            $dateFrom = $beginDate;
        }

        $this->getRentas();
        $this->renta_types[] = 'money';

        $farmingId = (int) $rpcParams['pbbn_farm'];
        $userFarmings = $FarmingController->getUserFarmings();
        $userFarmingIds = array_keys($userFarmings);
        $farmingIds = in_array($farmingId, $userFarmingIds) ? [$farmingId] : $userFarmingIds;

        // creating empty return
        // Create the default empty footer for row "Total for page"
        $paged_footer = [
            'owner_name' => '<b>Общо за стр.</b>',
        ];

        // Create the default empty footer for row "Total"
        $total_footer = [
            'owner_name' => '<b>Общо</b>',
            'c_num' => '',
        ];

        foreach ($this->renta_types as $renta_type) {
            $paged_footer[$renta_type] = '-';
            $total_footer[$renta_type] = '-';
        }

        $bankPaymentType = null;
        switch (true) {
            case ('post' == $rpcParams['pbbn_payment_type']):
                $paymentType = true;
                $bankPaymentType = Config::PAYMENT_TYPE_POST;
                $bankPaymentTypeCompare = '=';

                break;
            case ('bank' == $rpcParams['pbbn_payment_type']):
                $paymentType = true;
                $bankPaymentType = Config::PAYMENT_TYPE_BANK;
                $bankPaymentTypeCompare = '=';

                break;
            case ('false' == $rpcParams['pbbn_payment_type']):
                $paymentType = false;
                $bankPaymentType = Config::PAYMENT_TYPE_BANK;
                $bankPaymentTypeCompare = '=';

                break;
            default:
                $paymentType = $rpcParams['pbbn_payment_type'];
                $bankPaymentType = [Config::PAYMENT_TYPE_BANK, Config::PAYMENT_TYPE_POST];
                $bankPaymentTypeCompare = 'IN';

                break;
        }

        $return = ['rows' => [], 'total' => 0, 'footer' => [0 => $paged_footer, 1 => $total_footer]];

        // prepare the main SQL query parameters
        $options = [
            'return' => [
                't2.owner_name',
                'coalesce(max(t2.participate_in_contract)::bool, false) as participate_in_contract',
                'array_to_string(t2.c_num, \',\', \'\') as c_num',
                'json_agg(json_build_object(\'rent_name\',
                    (CASE WHEN t2.rent_name is not null THEN t2.rent_name ELSE \'money\' END), \'rent_value\', t2.amount, \'rent_unit\', t2.rent_unit)
                ) result',
            ],
            'where' => [
                'dateFrom' => ['column' => 'date', 'compare' => '>=', 'prefix' => 'p', 'value' => $dateFrom],
                'dateTo' => ['column' => 'date', 'compare' => '<=', 'prefix' => 'p', 'value' => $dateTo],
                'rent_place' => ['column' => 'rent_place', 'compare' => '=', 'prefix' => 'o', 'value' => $rpcParams['pbbn_place']],
                'bank_payment' => ['column' => 'bank_payment', 'compare' => '=', 'prefix' => 't', 'value' => $paymentType],
                'bank_payment_type' => ['column' => 'bank_payment_type', 'compare' => $bankPaymentTypeCompare, 'prefix' => 't', 'value' => $bankPaymentType],
                'owner_type' => ['column' => 'owner_type', 'compare' => '=', 'prefix' => 'o', 'value' => $rpcParams['pbbn_owner_type']],
                'rentaType' => ['column' => 'nat_type', 'compare' => '=', 'prefix' => 'pn', 'value' => $rpcParams['pbbn_renta_type']],
                'year' => ['column' => 'farming_year', 'compare' => '=', 'prefix' => 't', 'value' => $rpcParams['pbbn_year']],
                'bank_acc' => ['column' => 'bank_acc', 'compare' => 'ILIKE', 'prefix' => 't', 'value' => $rpcParams['pbbn_bank_acc']],
                'recipient' => ['column' => 'recipient', 'compare' => '@@', 'prefix' => 't', 'value' => $rpcParams['pbbn_paid_to']],
                'payer_name' => ['column' => 'payer_name', 'compare' => '@@', 'prefix' => 't', 'value' => $rpcParams['pbbn_paid_by']],
                'farming_ids' => [
                    'column' => '(case when a.id is not null then a.farming_id else c.farming_id end)',
                    'compare' => 'IN',
                    'value' => $farmingIds,
                ],
            ],
        ];

        if (!empty($rpcParams['pbbn_ekattes']) && '' !== $rpcParams['pbbn_ekattes'][0]) {
            $options['ekattes'] = $rpcParams['pbbn_ekattes'];
        }

        if (!empty($rpcParams['pbbn_cnum'])) {
            $options['c_num'] = $rpcParams['pbbn_cnum'];
        }

        // get the total results, without pagination
        $total_results = $UserDbPaymentsController->getPaymentsByBankAndNatura($options, false, false);

        // Check if any results will be available for further processing
        if (0 == count($total_results)) {
            return $return;
        }

        if ($page && $rows) {
            $options['offset'] = ($page - 1) * $rows;
            $options['limit'] = (int)$rows;
        }

        $options['sort'] = $sort;
        $options['order'] = $order;

        // get the actual results
        $results = $UserDbPaymentsController->getPaymentsByBankAndNatura($options, false, false);

        // loop through all the results to beautify the output
        foreach ($results as &$result) {
            $result = $this->transformResults($result, false);
        }
        unset($result);

        if ('owner_name' !== $sort) {
            usort($results, function ($a, $b) use ($sort, $order) {
                if ((float)$a[$sort] == (float)$b[$sort]) {
                    return 0;
                }

                if ('asc' == $order) {
                    return ((float)$a[$sort] < (float)$b[$sort]) ? -1 : 1;
                }

                return ((float)$a[$sort] > (float)$b[$sort]) ? -1 : 1;
            });
        }

        foreach ($this->total as $key => $total) {
            if ('money' == $key) {
                $paged_footer[$key] = BGNtoEURO($total);
            } else {
                $paged_footer[$key] = number_format($total, 3, '.', '');
            }
        }

        // loop through all the results to beautify the output
        foreach ($total_results as $total_result) {
            $this->transformResults($total_result, true);
        }
        unset($total_result);

        foreach ($this->totalAll as $key => $total) {
            if ('money' == $key) {
                $total_footer[$key] = BGNtoEURO($total);
            } else {
                $total_footer[$key] = number_format($total, 3, '.', '');
            }
        }

        $return['rows'] = $results;
        $return['total'] = count($total_results);
        $return['footer'] = [0 => $paged_footer, 1 => $total_footer];

        return $return;
    }

    public function exportPaymentsBankAndNaturaPaymentReport($rpcParams, $sort = '', $order = '')
    {
        // Initialize the method controllers
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);

        // Get the filtered results, using the getPaymentsBankAndNaturaPaymentReport method
        $resultsToBePrinted = $this->getPaymentsBankAndNaturaPaymentReport($rpcParams, null, null, $sort, $order);
        unset($resultsToBePrinted['total']);

        $headerRow = [
            'owner_name' => 'Собственик',
            'c_num' => 'Номер на договора',
        ];

        foreach ($this->renta_types as $keyRenta => $renta_type) {
            if ('money' != $renta_type) {
                $headerRow[$renta_type] = $renta_type . $this->renta_types_units[$keyRenta];
            }
        }
        $headerRow['money'] = 'В лева';

        $footer[] = array_values($resultsToBePrinted['footer'][1]);
        $finalResultsArray = [];

        $headerKeys = array_keys($headerRow);
        foreach ($resultsToBePrinted['rows'] as $resultToBePrinted) {
            // Filter the results to only include the columns that are in the header
            $row = array_filter($resultToBePrinted, function ($key) use ($headerKeys) {
                return in_array($key, $headerKeys);
            }, ARRAY_FILTER_USE_KEY);

            $finalResultsArray[] = $row;
        }

        // Add timestamp to ensure unique file names
        $date = date('Y-m-d-H-i-s');

        // set the file name as variable
        $name = str_replace('/', '_', 'bank_payment_' . $this->User->UserID . '_' . $date);

        $name .= '.xlsx';
        // set the file path
        $path = PUBLIC_UPLOAD_BLANK . '/' . $this->User->GroupID . '/';

        // create the directory if it doesn't exist
        if (!is_dir($path)) {
            mkdir($path, 0774);
        }

        // create the return array
        $return = [];

        $exportExcelDoc = new ExportToExcelClass();
        $exportExcelDoc->export($finalResultsArray, $headerRow, $footer);
        $exportExcelDoc->saveFile($path . $name);

        // Set the return variables
        $return['file_path'] = PUBLIC_UPLOAD_BLANKS_RELATIVE_PATH . $this->User->GroupID . '/' . $name;
        $return['file_name'] = $name;

        return $return;
    }

    /**
     * Helper method used to transform the results by
     * formating the numbers, replacing zero values with '-',
     * setting names for the nat columns, etc.
     */
    private function transformResults($result, $all = false)
    {
        $rentaResults = json_decode($result['result']);
        foreach ($this->renta_types as $renta_type) {
            $result[$renta_type] = '-';
        }
        unset($result['result']);

        foreach ($rentaResults as $rentaResult) {
            if ('money' == $rentaResult->rent_name) {
                $result[$rentaResult->rent_name] = BGNtoEURO($rentaResult->rent_value);
            } else {
                $result[$rentaResult->rent_name] = number_format($rentaResult->rent_value, 3, '.', '');
            }

            $all ? $this->totalAll[$rentaResult->rent_name] += $rentaResult->rent_value : $this->total[$rentaResult->rent_name] += $rentaResult->rent_value;
        }

        return $result;
    }

    /**
     * Helper method to get all the renta names, units and unit values.
     */
    private function getRentas()
    {
        $UserDbController = new UserDbController($this->User->Database);
        $options = [
            'tablename' => $UserDbController->DbHandler->tableRentaTypes,
        ];
        $renta_results = $UserDbController->getItemsByParams($options, false, false);
        $rentCount = count($renta_results);
        // create renta types array
        for ($i = 0; $i < $rentCount; $i++) {
            // this array will hold all the names of all renta types in key-value pairs (key is the ID, value is the name)
            $this->renta_types[$renta_results[$i]['id']] = $renta_results[$i]['name'];

            // this array will hold all the units of the renta types in key-value pairs (key is the ID, value is the type)
            $this->renta_types_units[$renta_results[$i]['id']] = '(' . $GLOBALS['Contracts']['renta_units'][$renta_results[$i]['unit']]['name'] . ')';

            // this array will hold all the amounts of the renta types in key-value pairs (key is the ID, value is the amount)
            $this->renta_types_values[$renta_results[$i]['id']] = $renta_results[$i]['unit_value'];
        }
    }
}
