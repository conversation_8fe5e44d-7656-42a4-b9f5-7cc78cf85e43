<?php

namespace TF\Engine\APIClasses\Payments;

use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Common\Config;
use TF\Engine\Kernel\ExportToExcelClass;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbPayments\UserDbPaymentsController;

/**
 * Справка Изплатено в брой/по банков път
 *
 * @rpc-module Payments
 *
 * @rpc-service-id payments-bank-payment-report
 */
class PaymentsBankPaymentReport extends TRpcApiProvider
{
    // Initialize helper variables
    private $renta_types = [];
    private $renta_types_units = [];
    private $renta_types_values = [];

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getPaymentsBankPaymentReport'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'export' => ['method' => [$this, 'exportPaymentsBankPaymentReport']],
        ];
    }

    /**
     * Метод за попълване на грида.
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer pbb_year
     *                         #item date pbb_date_from
     *                         #item date pbb_date_to
     *                         #item boolean pbb_payment_type
     *                         #item integer pbb_renta_type
     *                         #item string pbb_paid_to
     *                         #item string pbb_paid_by
     *                         #item string pbb_bank_acc
     *                         }
     * @param int $page pagination parameter
     * @param int $rows pagination parameter
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array
     *               {
     *               #item array rows
     *               {
     *               #item double amount
     *               #item string bank_acc
     *               #item boolean bank_payment
     *               #item date date
     *               #item string farming_year
     *               #item string full_renta
     *               #item double nat_amount
     *               #item string nat_name
     *               #item integer nat_type
     *               #item string nat_unit
     *               #item integer paid_from
     *               #item integer paid_in
     *               #item string payer_name
     *               #item string payment_type
     *               #item string recipient
     *               #item double renta_value
     *               #item integer transaction_id
     *               }
     *               #item integer total
     *               #item array footer
     *               {
     *               #item array 0
     *               {
     *               #item string bank_acc
     *               #item double amount
     *               #item double nat_amount
     *               #item string full_renta
     *               }
     *               #item array 1
     *               {
     *               #item string bank_acc
     *               #item double amount
     *               #item double nat_amount
     *               #item string full_renta
     *               }
     *               }
     *               }
     */
    public function getPaymentsBankPaymentReport(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        // Initialize the method controllers
        $FarmingController = new FarmingController('Farming');
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);

        // Simplify the used parameters
        $farmingYear = $rpcParams['pbb_year'];
        $dateFrom = $rpcParams['pbb_date_from'];
        $dateTo = $rpcParams['pbb_date_to'];
        $farmingId = $rpcParams['pbb_farm'];
        $userFarmings = $FarmingController->getUserFarmings();
        $userFarmingIds = array_keys($userFarmings);
        $farmingIds = in_array($farmingId, $userFarmingIds) ? [$farmingId] : $userFarmingIds;

        switch (true) {
            case ('post' == $rpcParams['pbb_payment_type']):
                $paymentType = true;
                $bankPaymentType = Config::PAYMENT_TYPE_POST;
                $bankPaymentTypeCompare = '=';

                break;
            case ('bank' == $rpcParams['pbb_payment_type']):
                $paymentType = true;
                $bankPaymentType = Config::PAYMENT_TYPE_BANK;
                $bankPaymentTypeCompare = '=';

                break;
            case ('false' == $rpcParams['pbb_payment_type']):
                $paymentType = false;
                $bankPaymentType = Config::PAYMENT_TYPE_BANK;
                $bankPaymentTypeCompare = '=';

                break;
            default:
                $paymentType = $rpcParams['pbb_payment_type'];
                $bankPaymentType = [Config::PAYMENT_TYPE_BANK, Config::PAYMENT_TYPE_POST];
                $bankPaymentTypeCompare = 'IN';

                break;
        }

        $rentaType = $rpcParams['pbb_renta_type'];
        $paidTo = $rpcParams['pbb_paid_to'];
        $paidBy = $rpcParams['pbb_paid_by'];
        $bankAcc = $rpcParams['pbb_bank_acc'];

        // Тази дата се използва за начална дата на справката.
        // Не трябва да се визуализират данни за плащания
        // Правени преди тази дата.
        $beginDate = '2016-01-01';
        // Прави се проверка дали има зададена начална дата на справката
        // Ако има начална дата, то се прави проверка дали е преди 01 Януари 2016
        // и ако не е се слага за начална дата 01 Януари 2016
        // След това се прави проверка дали крайната дата е по-голяма от началната
        // и в случай, че не е се добавят 7 дни и по този начин справката е за период от 7 дни след началната дата
        if ($dateFrom) {
            $dateFrom = $dateFrom >= $beginDate ? $dateFrom : $beginDate;

            if ($dateTo) {
                $dateTo = $dateTo >= $dateFrom ? $dateTo : date('Y-m-d', strtotime($dateFrom . ' + 7 days'));
            }

            // Ако няма зададена начална дата
            // изрично се задава начална дата на справката
            // $beginDate, която е зададена да бъде 01 Януари 2016г.
            // За да не се връщат резултати преди тази дата
        } else {
            $dateFrom = $beginDate;
        }

        // creating empty return
        // Create the default empty footer for row "Total for page"
        $paged_footer = [
            'unit_value' => 'Общо за стр.',
            'amount' => number_format(0, 2, '.', ' '),
            'nat_amount' => '-',
            'full_renta' => '-',
        ];

        // Create the default empty footer for row "Total"
        $total_footer = [
            'unit_value' => 'Общо',
            'amount' => number_format(0, 2, '.', ' '),
            'nat_amount' => '-',
            'full_renta' => '-',
        ];
        $return = ['rows' => [], 'total' => 0, 'footer' => [0 => $paged_footer, 1 => $total_footer]];

        // get renta types from the DB
        $this->getRentas();

        // prepare the main SQL query parameters
        $options = [
            'return' => [
                't.ID AS transaction_id', 'string_agg(distinct c.c_num, \', \') as c_num', 'p.date', 't.recipient', 't.payer_name', 't.bank_acc', 't.farming_year',
                'pn.nat_type', 'sum(pn.amount) as nat_amount', 'p.paid_from', 'p.paid_in', 't.bank_payment', 't.bank_payment_type',
                'pn.unit_value',
                'CASE WHEN pn.nat_type NOTNULL
                    THEN sum(pn.amount) * pn.unit_value
                    ELSE sum(p.amount)
                END as amount',
                '(sum(contracts.is_exists) > 0) as participate_in_contract',
            ],
            'where' => [
                'paid_in' => ['column' => 'paid_in', 'compare' => '=', 'prefix' => 'p', 'value' => 1],
                'year' => ['column' => 'farming_year', 'compare' => '=', 'prefix' => 't', 'value' => $farmingYear],
                'dateFrom' => ['column' => 'date', 'compare' => '>=', 'prefix' => 'p', 'value' => $dateFrom],
                'dateTo' => ['column' => 'date', 'compare' => '<=', 'prefix' => 'p', 'value' => $dateTo],
                'bank_payment' => ['column' => 'bank_payment', 'compare' => '=', 'prefix' => 't', 'value' => $paymentType],
                'bank_payment_type' => ['column' => 'bank_payment_type', 'compare' => $bankPaymentTypeCompare, 'prefix' => 't', 'value' => $bankPaymentType],
                'rentaType' => ['column' => 'nat_type', 'compare' => '=', 'prefix' => 'pn', 'value' => $rentaType],
                'bank_acc' => ['column' => 'bank_acc', 'compare' => 'ILIKE', 'prefix' => 't', 'value' => $bankAcc],
                'recipient' => ['column' => 'recipient', 'compare' => '@@', 'prefix' => 't', 'value' => $paidTo],
                'payer_name' => ['column' => 'payer_name', 'compare' => '@@', 'prefix' => 't', 'value' => $paidBy],
                'farm' => ['column' => 'farming_id', 'compare' => 'IN', 'prefix' => 'c', 'value' => $farmingIds],
                'owner_type' => ['column' => 'owner_type', 'compare' => '=', 'prefix' => 'o', 'value' => $rpcParams['pbb_owner_type']],
                'rent_place' => ['column' => 'rent_place', 'compare' => '=', 'prefix' => 'o', 'value' => $rpcParams['pbb_place']],
                'c_num' => ['column' => 'c_num', 'compare' => '=', 'prefix' => 'c', 'value' => $rpcParams['pbb_cnum']],
            ],
            'group' => 't.id, p.date, pn.nat_type, p.paid_from, p.paid_in, pn.unit_value',
            'annexes_no_join' => true,
            'joins' => [
                'left join lateral (
                    select
                        count(spor.id) as is_exists
                    from su_plots_owners_rel spor
                    left join su_contracts_plots_rel scpr on scpr.contract_id = c.id
                    where 
                        spor.owner_id = o.id
                        and ((spor."path" is null and p."path" is null) or spor."path" = p."path")
                ) contracts on true',
            ],
        ];
        if (!empty($rpcParams['pbb_ekattes']) && '' !== $rpcParams['pbb_ekattes'][0]) {
            $options['where']['ekatte'] = ['column' => 'ekate', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $rpcParams['pbb_ekattes']];
            $options['join_kvs'] = true;
        }
        // get the total results, without pagination
        $total_results = $UserDbPaymentsController->getPaymentsByParamsWithKvs($options, false, false);
        // Check if any results will be available for further processing
        if (0 == count($total_results)) {
            return $return;
        }

        $options['offset'] = ($page - 1) * $rows;
        $options['sort'] = $sort;
        $options['order'] = $order;
        $options['limit'] = $rows;

        // get the actual results
        $results = $UserDbPaymentsController->getPaymentsByParamsWithKvs($options, false, false);
        $resultsCount = count($results);

        // Initialize the page total row values
        $page_total_sum = 0;
        $page_total_nat = [];
        // loop through all the results to beautify the output
        for ($i = 0; $i < $resultsCount; $i++) {
            // Transform the results
            $results[$i] = $this->transformResults($results[$i]);

            // get the total sum for the page
            if (is_numeric($results[$i]['amount_value'])) {
                $page_total_sum += $results[$i]['amount_value'];
            }

            // create array that holds the natura types and amounts as key-value pair
            if (is_numeric($results[$i]['nat_amount'])) {
                $page_total_nat[$results[$i]['nat_type']] += $results[$i]['nat_amount'];
            }
        }

        // initialize empty helper variables
        $paged_footer_nat_text = '';
        $paged_footer_nat_sum = '';

        // loop through the total paged array to beautify the array output
        foreach ($page_total_nat as $key => $value) {
            // get only the positive values
            if (0 != $value && '' != $key) {
                // aggregate the natura type column
                $paged_footer_nat_text .= $this->renta_types[$key] . $this->renta_types_units[$key] . '<br/>';

                // aggregate the natura amount column
                $paged_footer_nat_sum .= number_format($value, 2, '.', ' ') . '<br/>';
            }
        }

        // assign the footer values for the paged row
        $paged_footer = [
            'unit_value' => 'Общо за стр.',
            'amount' => BGNtoEURO($page_total_sum),
            'nat_amount' => $paged_footer_nat_sum,
            'full_renta' => $paged_footer_nat_text,
        ];

        $total_footer = $this->populateFooter($total_results);
        // finalize the return statement
        $return['rows'] = $results;
        $return['total'] = count($total_results);
        $return['footer'] = [0 => $paged_footer, 1 => $total_footer];

        return $return;
    }

    /**
     * Метод за попълване на грида.
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer pbb_year
     *                         #item date pbb_date_from
     *                         #item date pbb_date_to
     *                         #item boolean pbb_payment_type
     *                         #item integer pbb_renta_type
     *                         #item string pbb_paid_to
     *                         #item string pbb_paid_by
     *                         #item string pbb_bank_acc
     *                         }
     * @param string $sort pagination parameter
     * @param string $order pagination parameter
     *
     * @return array
     *               {
     *               #item string path
     *               #item string name
     *               }
     */
    public function exportPaymentsBankPaymentReport($rpcParams, $sort = '', $order = '')
    {
        // Initialize the method controllers
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);

        // Get the filtered results, using the getPaymentsBankPaymentReport method
        $resultsToBePrinted = $this->getPaymentsBankPaymentReport($rpcParams, null, null, $sort, $order);

        unset($resultsToBePrinted['total']);

        // set the header columns
        $headerRow[0] = 'Номер на транзакция';
        $headerRow[1] = 'Номер на договора';
        $headerRow[2] = 'Дата';
        $headerRow[3] = 'Начин на плащане';
        $headerRow[4] = 'Изплатено на';
        $headerRow[5] = 'Изплатено от';
        $headerRow[6] = 'Стопанска година';
        $headerRow[7] = 'Банкова сметка';
        $headerRow[8] = 'Единична цена';
        $headerRow[9] = 'Сума (лв.)';
        $headerRow[10] = 'Количество натура';
        $headerRow[11] = 'Тип натура';

        $footer[0][8] = $resultsToBePrinted['footer'][1]['unit_value'];
        $footer[0][9] = $resultsToBePrinted['footer'][1]['amount'];
        $footer[0][10] = str_replace('<br/>', ', ', $resultsToBePrinted['footer'][1]['nat_amount']);
        $footer[0][11] = str_replace('<br/>', ', ', $resultsToBePrinted['footer'][1]['full_renta']);

        // Loop through the remaining elements and transform them into sequential array
        // so to be able to match the result columns with the header columns
        $finalResultsArray = [];
        $rowsCount = count($resultsToBePrinted['rows']);
        for ($i = 0; $i < $rowsCount; $i++) {
            // Assign the necessary columns their respective indexes
            $finalResultsArray[$i] = $this->extractAndSortColumns($resultsToBePrinted['rows'][$i]);
        }

        // Set the header row as first array element
        // array_unshift($finalResultsArray, $headerRow);

        // Add timestamp to ensure unique file names
        $date = date('Y-m-d-H-i-s');

        // set the file name as variable
        $name = str_replace('/', '_', 'bank_payment_' . $this->User->UserID . '_' . $date);
        $name .= '.xlsx';
        // set the file path
        $path = PUBLIC_UPLOAD_BLANK . '/' . $this->User->GroupID . '/';

        // create the directory if it doesn't exist
        if (!is_dir($path)) {
            mkdir($path, 0774);
        }

        // create the return array
        $return = [];

        $exportExcelDoc = new ExportToExcelClass();
        $exportExcelDoc->export($finalResultsArray, $headerRow, $footer);
        $exportExcelDoc->saveFile($path . $name);

        // Set the return variables
        $return['file_path'] = PUBLIC_UPLOAD_BLANKS_RELATIVE_PATH . $this->User->GroupID . '/' . $name;
        $return['file_name'] = $name;

        return $return;
    }

    /**
     * Helper method used to transform the results by
     * formating the numbers, replacing zero values with '-',
     * setting names for the nat columns, etc.
     */
    private function transformResults($result)
    {
        // format the natura amount as double precision number
        $result['nat_amount'] = 0 != $result['nat_amount'] ? number_format($result['nat_amount'], 2, '.', '') : '-';

        // format the money amount as double precision number
        $result['amount_value'] = 0 != $result['amount'] ? number_format($result['amount'], 2, '.', '') : '-';
        $result['amount'] = 0 != $result['amount'] ? BGNtoEURO($result['amount']) : '-';

        // get the natura name
        $result['nat_name'] = $this->renta_types[$result['nat_type']];

        // get the natura unit
        $result['nat_unit'] = $this->renta_types_units[$result['nat_type']];

        // get the natura type
        $result['renta_value'] = $this->renta_types_values[$result['nat_type']];

        // concatenate the final natura output as natura name (natura unit) or put dash if empty
        $result['full_renta'] = $result['nat_name'] ? $result['nat_name'] . ' ' . $result['nat_unit'] : '-';

        // format the date
        $result['date'] = strftime('%d.%m.%Y', strtotime($result['date']));

        // get the farming year as text
        $result['farming_year'] = $GLOBALS['Farming']['years'][$result['farming_year']]['farming_year_short'];

        // get the bank account or put dash if empty
        $result['bank_acc'] = $result['bank_acc'] ? $result['bank_acc'] : '-';

        // set the payment type according to the boolean value
        $result['payment_type'] = $result['bank_payment'] ? (Config::PAYMENT_TYPE_BANK == $result['bank_payment_type'] ? 'Банков път' : 'Пощенски запис') : 'В брой';

        // set the single unit price if converted
        $result['unit_value'] = 0 != $result['unit_value'] ? BGNtoEURO($result['unit_value']) : '-';

        return $result;
    }

    /**
     * Helper method used to get all data for the
     * total footer row.
     */
    private function populateFooter($total_results)
    {
        // initialize helper variables
        $total_footer_nat_text = '';
        $total_footer_nat_sum = '';
        $total_nats = [];

        // loop through all results and get the amout for each nat as key=>value pair
        // where the nat type is key and the amount is the value
        // and remove the empty nats
        foreach ($total_results as $result) {
            if ('' != $result['nat_type']) {
                $total_nats[$result['nat_type']] += $result['nat_amount'];
            }
        }

        // loop through the final nats array to
        // assign names to each nat type and also to format the resulting number
        foreach ($total_nats as $key => $value) {
            // aggregate the natura type column
            $total_footer_nat_text .= $this->renta_types[$key] . $this->renta_types_units[$key] . '<br/>';
            // aggregate the natura amount column
            $total_footer_nat_sum .= number_format($value, 2, '.', '') . '<br/>';
        }

        // get the money sum for the whole report
        $total_sum = 0;

        foreach ($total_results as $result) {
            $total_sum += $result['amount'];
        }

        // assign the footer's total row the proper values
        return [
            'unit_value' => 'Общо',
            'amount' => BGNtoEURO($total_sum),
            'nat_amount' => $total_footer_nat_sum,
            'full_renta' => $total_footer_nat_text,
        ];
    }

    /**
     * Helper method which is used to get only the neccessary for printing columns
     * and to arrange them in a sequential array, in the required order.
     *
     * @param array $currentRow
     *
     * @return array
     */
    private function extractAndSortColumns($currentRow)
    {
        // Initialize the return value
        $sortedArray = [];

        // Get only the necessary columns and sort them accordingly
        $sortedArray[0] = $currentRow['transaction_id'];
        $sortedArray[1] = $currentRow['c_num'];
        $sortedArray[2] = $currentRow['date'];
        $sortedArray[3] = $currentRow['payment_type'];
        $sortedArray[4] = $currentRow['recipient'];
        $sortedArray[5] = $currentRow['payer_name'];
        $sortedArray[6] = $currentRow['farming_year'];
        $sortedArray[7] = $currentRow['bank_acc'];
        $sortedArray[8] = $currentRow['unit_value'];
        $sortedArray[9] = $currentRow['amount'];
        $sortedArray[10] = $currentRow['nat_amount'];
        $sortedArray[11] = $currentRow['full_renta'];

        return $sortedArray;
    }

    /**
     * Helper method to get all the renta names, units and unit values.
     */
    private function getRentas()
    {
        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableRentaTypes,
        ];
        $renta_results = $UserDbController->getItemsByParams($options, false, false);
        $rentCount = count($renta_results);

        // create renta types array
        for ($i = 0; $i < $rentCount; $i++) {
            // this array will hold all the names of all renta types in key-value pairs (key is the ID, value is the name)
            $this->renta_types[$renta_results[$i]['id']] = $renta_results[$i]['name'];

            // this array will hold all the units of the renta types in key-value pairs (key is the ID, value is the type)
            $this->renta_types_units[$renta_results[$i]['id']] = '(' . $GLOBALS['Contracts']['renta_units'][$renta_results[$i]['unit']]['name'] . ')';

            // this array will hold all the amounts of the renta types in key-value pairs (key is the ID, value is the amount)
            $this->renta_types_values[$renta_results[$i]['id']] = $renta_results[$i]['unit_value'];
        }
    }
}
